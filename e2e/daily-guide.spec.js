import { test, expect } from '@playwright/test';
test.describe('Daily Guide Feature', () => {
    test('should display daily guide page with all sections', async ({ page }) => {
        // Navigate to the daily guide page
        await page.goto('/daily-guide');
        // Check that the page title is displayed
        await expect(page.locator('h1')).toContainText('Daily Trading Guide');
        // Check that all sections are displayed
        await expect(page.getByText('Market Overview')).toBeVisible();
        await expect(page.getByText('Trading Plan')).toBeVisible();
        await expect(page.getByText('Key Levels')).toBeVisible();
        await expect(page.getByText('Market News')).toBeVisible();
        // Check that the refresh button works
        const refreshButton = page.getByRole('button', { name: 'Refresh' });
        await expect(refreshButton).toBeVisible();
        await refreshButton.click();
        // Wait for data to reload
        await page.waitForTimeout(1000);
    });
    test('should allow interaction with trading plan items', async ({ page }) => {
        // Navigate to the daily guide page
        await page.goto('/daily-guide');
        // Find a trading plan item checkbox
        const checkbox = page.locator('input[type="checkbox"]').first();
        // Check the initial state
        const initialChecked = await checkbox.isChecked();
        // Toggle the checkbox
        await checkbox.click();
        // Verify the checkbox state changed
        await expect(checkbox).toBeChecked(!initialChecked);
        // Toggle it back
        await checkbox.click();
        // Verify it's back to the initial state
        await expect(checkbox).toBeChecked(initialChecked);
    });
});
//# sourceMappingURL=daily-guide.spec.js.map