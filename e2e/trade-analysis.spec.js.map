{"version": 3, "file": "trade-analysis.spec.js", "sourceRoot": "", "sources": ["trade-analysis.spec.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,kBAAkB,CAAC;AAGhD,IAAI,CAAC,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;IAC3C,IAAI,CAAC,sDAAsD,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;QAC9E,sCAAsC;QACtC,MAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEnC,yCAAyC;QACzC,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;QAEjE,2CAA2C;QAC3C,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAEtD,sDAAsD;QACtD,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAClE,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QACzE,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAEzE,8BAA8B;QAC9B,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;QAC3D,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QACrD,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAExD,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;QAC5D,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAEpE,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;QAC/D,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAEtE,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;QAC/D,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QACvE,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAErE,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;QAClE,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QACzE,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;IAC3E,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,kCAAkC,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;QAC1D,sCAAsC;QACtC,MAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEnC,+CAA+C;QAC/C,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,KAAK,EAAE,CAAC;QAClE,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAE/D,+BAA+B;QAC/B,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;QACzB,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;QAClC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;QAEzC,MAAM,SAAS,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB;QAC9E,MAAM,OAAO,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB;QAExE,MAAM,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACrC,MAAM,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEjC,gBAAgB;QAChB,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;QAElE,0BAA0B;QAC1B,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAEhC,gBAAgB;QAChB,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;QAE1D,0BAA0B;QAC1B,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,uDAAuD,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;QAC/E,sCAAsC;QACtC,MAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEnC,wBAAwB;QACxB,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC;QAE3D,0BAA0B;QAC1B,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAEhC,+BAA+B;QAC/B,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;QAExC,yCAAyC;QACzC,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QAE5D,gDAAgD;QAChD,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;QAExC,mDAAmD;QACnD,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;IAClE,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}