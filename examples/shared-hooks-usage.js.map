{"version": 3, "file": "shared-hooks-usage.js", "sourceRoot": "", "sources": ["shared-hooks-usage.tsx"], "names": [], "mappings": ";AAQA,OAAO,EACL,eAAe,EACf,YAAY,EACZ,cAAc,EACd,uBAAuB,EACvB,iBAAiB,EACjB,eAAe,EACf,iBAAiB,EACjB,cAAc,EACd,aAAa,EACd,MAAM,gCAAgC,CAAC;AAExC,+EAA+E;AAC/E,kCAAkC;AAClC,+EAA+E;AAE/E;;;;;GAKG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAa,GAAG,EAAE;IAChD,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,eAAe,EAAE,CAAC;IAE5D,MAAM,WAAW,GAAG,KAAK,IAAI,EAAE;QAC7B,+DAA+D;QAC/D,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,KAAK,IAAI,EAAE;YAC1C,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,aAAa,CAAC,CAAC;YAC5C,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAAE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC5D,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;IACxC,CAAC,CAAC;IAEF,OAAO,CACL,0BACE,iDAA8B,EAC9B,iBAAQ,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,YAC9C,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,cAAc,GACnC,EACR,KAAK,IAAI,eAAK,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,wBAAU,KAAK,IAAO,IACxD,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,+EAA+E;AAC/E,+CAA+C;AAC/C,+EAA+E;AAE/E;;;;;GAKG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAa,GAAG,EAAE;IAC7C,MAAM,UAAU,GAAG,YAAY,CAAC;QAC9B,YAAY,EAAE,EAAE;QAChB,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,OAAO;QACb,eAAe,EAAE;YACf,eAAe,CAAC,QAAQ,CAAC,mBAAmB,CAAC;YAC7C,eAAe,CAAC,KAAK,CAAC,4BAA4B,CAAC;SACpD;QACD,cAAc,EAAE,IAAI;KACrB,CAAC,CAAC;IAEH,MAAM,WAAW,GAAG,YAAY,CAAC;QAC/B,YAAY,EAAE,CAAC;QACf,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,QAAQ;QACd,eAAe,EAAE;YACf,eAAe,CAAC,QAAQ,CAAC,oBAAoB,CAAC;YAC9C,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,+BAA+B,CAAC;SAC3D;QACD,gBAAgB,EAAE,IAAI;KACvB,CAAC,CAAC;IAEH,MAAM,YAAY,GAAG,KAAK,EAAE,CAAkB,EAAE,EAAE;QAChD,CAAC,CAAC,cAAc,EAAE,CAAC;QAEnB,sBAAsB;QACtB,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,QAAQ,EAAE,CAAC;QAC/C,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,QAAQ,EAAE,CAAC;QAEjD,IAAI,UAAU,IAAI,WAAW,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE;gBACxB,KAAK,EAAE,UAAU,CAAC,KAAK;gBACvB,MAAM,EAAE,WAAW,CAAC,KAAK;aAC1B,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CACL,gBAAM,QAAQ,EAAE,YAAY,aAC1B,8CAA2B,EAG3B,0BACE,qCAAqB,EACrB,gBACE,IAAI,EAAC,OAAO,EACZ,KAAK,EAAE,UAAU,CAAC,KAAK,EACvB,QAAQ,EAAE,UAAU,CAAC,YAAY,EACjC,MAAM,EAAE,UAAU,CAAC,UAAU,EAC7B,KAAK,EAAE,EAAE,WAAW,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,GACzD,EACD,UAAU,CAAC,KAAK,IAAI,UAAU,CAAC,OAAO,IAAI,CACzC,cAAK,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAG,UAAU,CAAC,KAAK,GAAO,CACzE,IACG,EAGN,KAAC,iBAAiB,IAChB,IAAI,EAAC,QAAQ,EACb,KAAK,EAAC,cAAc,EACpB,IAAI,EAAC,QAAQ,EACb,QAAQ,EAAE,IAAI,EACd,eAAe,EAAE;oBACf,eAAe,CAAC,QAAQ,CAAC,oBAAoB,CAAC;oBAC9C,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,+BAA+B,CAAC;iBAC3D,EACD,WAAW,EAAC,oBAAoB,EAChC,QAAQ,EAAC,+BAA+B,GACxC,EAEF,iBAAQ,IAAI,EAAC,QAAQ,uBAAgB,IAChC,CACR,CAAC;AACJ,CAAC,CAAC;AAEF,+EAA+E;AAC/E,iCAAiC;AACjC,+EAA+E;AAE/E;;;;;GAKG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAa,GAAG,EAAE;IAC/C,MAAM,aAAa,GAAG,cAAc,CAAC;QACnC,SAAS,EAAE,KAAK,IAAI,EAAE;YACpB,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,aAAa,CAAC,CAAC;YAC5C,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAAE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC5D,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;QACzB,CAAC;QACD,YAAY,EAAE,IAAI;QAClB,eAAe,EAAE,KAAK,EAAE,2BAA2B;QACnD,OAAO,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;KACpD,CAAC,CAAC;IAEH,IAAI,aAAa,CAAC,SAAS,EAAE,CAAC;QAC5B,OAAO,KAAC,cAAc,IAAC,IAAI,EAAC,IAAI,GAAG,CAAC;IACtC,CAAC;IAED,IAAI,aAAa,CAAC,KAAK,EAAE,CAAC;QACxB,OAAO,CACL,0BACE,eAAK,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,wBAAU,aAAa,CAAC,KAAK,IAAO,EAChE,iBAAQ,OAAO,EAAE,aAAa,CAAC,OAAO,sBAAgB,IAClD,CACP,CAAC;IACJ,CAAC;IAED,IAAI,aAAa,CAAC,OAAO,EAAE,CAAC;QAC1B,OAAO,4CAA0B,CAAC;IACpC,CAAC;IAED,OAAO,CACL,0BACE,gDAA6B,EAC7B,0BACE,iBAAQ,OAAO,EAAE,aAAa,CAAC,OAAO,wBAAkB,EACxD,6CAAqB,aAAa,CAAC,WAAW,EAAE,kBAAkB,EAAE,IAAQ,IACxE,EACN,oCAAY,aAAa,CAAC,IAAI,EAAE,MAAM,eAAc,IAChD,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,+EAA+E;AAC/E,0CAA0C;AAC1C,+EAA+E;AAE/E;;;;;GAKG;AACH,MAAM,CAAC,MAAM,2BAA2B,GAAa,GAAG,EAAE;IACxD,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,GAAG,iBAAiB,EAAE,CAAC;IAE9D,MAAM,MAAM,GAAG,uBAAuB,CAAC,OAAO,EAAE;QAC9C,QAAQ,EAAE,GAAG;QACb,gBAAgB,EAAE,IAAI;KACvB,CAAC,CAAC;IAEH,MAAM,IAAI,GAAG,uBAAuB,CAAC,CAAC,MAAM,EAAE;QAC5C,QAAQ,EAAE,GAAG;KACd,CAAC,CAAC;IAEH,MAAM,OAAO,GAAG,uBAAuB,CAAC,CAAC,EAAE;QACzC,QAAQ,EAAE,GAAG;KACd,CAAC,CAAC;IAEH,OAAO,CACL,0BACE,0DAAuC,EAEvC,eAAK,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,yBAC7C,MAAM,CAAC,eAAe,IAC3B,EAEN,eAAK,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,EAAE,uBAC3C,IAAI,CAAC,eAAe,IACvB,EAEN,eAAK,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,0BACjB,OAAO,CAAC,eAAe,IAC7B,EAEN,0BACE,qDAAkC,EAClC,wCAAgB,cAAc,CAAC,OAAO,CAAC,IAAO,EAC9C,0CAAkB,aAAa,CAAC,IAAI,CAAC,IAAO,EAC5C,oCAAY,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE,IAAO,IAC9C,IACF,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,+EAA+E;AAC/E,qDAAqD;AACrD,+EAA+E;AAE/E;;;;GAIG;AACH,MAAM,CAAC,MAAM,eAAe,GAAa,GAAG,EAAE;IAC5C,MAAM,EAAE,cAAc,EAAE,GAAG,iBAAiB,EAAE,CAAC;IAE/C,MAAM,UAAU,GAAG,cAAc,CAAC;QAChC,SAAS,EAAE,KAAK,IAAI,EAAE;YACpB,oBAAoB;YACpB,OAAO;gBACL,EAAE,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE;gBACnE,EAAE,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,YAAY,EAAE;gBACnE,EAAE,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE;aACpE,CAAC;QACJ,CAAC;QACD,YAAY,EAAE,IAAI;KACnB,CAAC,CAAC;IAEH,MAAM,OAAO,GAAG;QACd,EAAE,KAAK,EAAE,QAAiB,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE;QAC7D,EAAE,KAAK,EAAE,YAAqB,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE;QAC9D,EAAE,KAAK,EAAE,MAAe,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE;KAC1D,CAAC;IAEF,MAAM,UAAU,GAAG,CAAC,KAAU,EAAE,GAAQ,EAAE,MAAW,EAAE,EAAE;QACvD,IAAI,MAAM,CAAC,KAAK,KAAK,YAAY,EAAE,CAAC;YAClC,OAAO,CACL,eAAM,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,YAChD,cAAc,CAAC,KAAK,EAAE,EAAE,gBAAgB,EAAE,IAAI,EAAE,CAAC,GAC7C,CACR,CAAC;QACJ,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAEF,IAAI,UAAU,CAAC,SAAS;QAAE,OAAO,KAAC,cAAc,KAAG,CAAC;IACpD,IAAI,UAAU,CAAC,KAAK;QAAE,OAAO,qCAAa,UAAU,CAAC,KAAK,IAAO,CAAC;IAElE,OAAO,CACL,0BACE,0DAAuC,EACvC,KAAC,aAAa,IACZ,IAAI,EAAE,UAAU,CAAC,IAAI,IAAI,EAAE,EAC3B,OAAO,EAAE,OAAO,EAChB,UAAU,EAAE,UAAU,EACtB,WAAW,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,EAAE,GACvD,IACE,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,+EAA+E;AAC/E,yBAAyB;AACzB,+EAA+E;AAE/E,MAAM,CAAC,MAAM,mBAAmB,GAAa,GAAG,EAAE;IAChD,OAAO,CACL,eAAK,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,aAChD,uDAAoC,EACpC,2LAGI,EAEJ,KAAC,mBAAmB,KAAG,EACvB,cAAM,EAEN,KAAC,gBAAgB,KAAG,EACpB,cAAM,EAEN,KAAC,kBAAkB,KAAG,EACtB,cAAM,EAEN,KAAC,2BAA2B,KAAG,EAC/B,cAAM,EAEN,KAAC,eAAe,KAAG,IACf,CACP,CAAC;AACJ,CAAC,CAAC;AAEF,eAAe,mBAAmB,CAAC"}