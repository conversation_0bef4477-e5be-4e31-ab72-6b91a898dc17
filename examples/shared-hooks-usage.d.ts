/**
 * Shared Hooks Usage Examples
 *
 * This file demonstrates how to use the new shared hooks created in Phase 1
 * of the refactoring plan. These examples show the patterns for the team.
 */
import React from 'react';
/**
 * Example: Using useLoadingState for consistent loading patterns
 *
 * BEFORE: Manual loading state management in every component
 * AFTER: Centralized loading logic with error handling
 */
export declare const LoadingStateExample: React.FC;
/**
 * Example: Using useFormField for consistent form behavior
 *
 * BEFORE: Manual form state and validation in every form
 * AFTER: Centralized form logic with built-in validation
 */
export declare const FormFieldExample: React.FC;
/**
 * Example: Using useDataSection for consistent data loading patterns
 *
 * BEFORE: Manual data fetching, loading, and error states in every component
 * AFTER: Centralized data section logic with automatic refresh
 */
export declare const DataSectionExample: React.FC;
/**
 * Example: Using useProfitLossFormatting for consistent P&L display
 *
 * BEFORE: Duplicate formatting logic in every component
 * AFTER: Centralized formatting with consistent styling
 */
export declare const ProfitLossFormattingExample: React.FC;
/**
 * Example: Complete component using multiple shared hooks
 *
 * This shows how the hooks work together to create a clean, maintainable component
 */
export declare const CompleteExample: React.FC;
export declare const SharedHooksExamples: React.FC;
export default SharedHooksExamples;
//# sourceMappingURL=shared-hooks-usage.d.ts.map