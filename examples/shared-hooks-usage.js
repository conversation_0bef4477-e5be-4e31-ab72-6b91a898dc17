import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useLoadingState, useFormField, useDataSection, useProfitLossFormatting, useDataFormatting, validationRules, EnhancedFormField, LoadingSpinner, SortableTable } from '@adhd-trading-dashboard/shared';
// ============================================================================
// EXAMPLE 1: useLoadingState Hook
// ============================================================================
/**
 * Example: Using useLoadingState for consistent loading patterns
 *
 * BEFORE: Manual loading state management in every component
 * AFTER: Centralized loading logic with error handling
 */
export const LoadingStateExample = () => {
    const { isLoading, error, withLoading } = useLoadingState();
    const fetchTrades = async () => {
        // The withLoading wrapper automatically handles loading states
        const trades = await withLoading(async () => {
            const response = await fetch('/api/trades');
            if (!response.ok)
                throw new Error('Failed to fetch trades');
            return response.json();
        });
        console.log('Trades loaded:', trades);
    };
    return (_jsxs("div", { children: [_jsx("h3", { children: "Loading State Example" }), _jsx("button", { onClick: fetchTrades, disabled: isLoading, children: isLoading ? 'Loading...' : 'Fetch Trades' }), error && _jsxs("div", { style: { color: 'red' }, children: ["Error: ", error] })] }));
};
// ============================================================================
// EXAMPLE 2: useFormField Hook with Validation
// ============================================================================
/**
 * Example: Using useFormField for consistent form behavior
 *
 * BEFORE: Manual form state and validation in every form
 * AFTER: Centralized form logic with built-in validation
 */
export const FormFieldExample = () => {
    const emailField = useFormField({
        initialValue: '',
        required: true,
        type: 'email',
        validationRules: [
            validationRules.required('Email is required'),
            validationRules.email('Please enter a valid email'),
        ],
        validateOnBlur: true,
    });
    const amountField = useFormField({
        initialValue: 0,
        required: true,
        type: 'number',
        validationRules: [
            validationRules.required('Amount is required'),
            validationRules.min(0.01, 'Amount must be greater than 0'),
        ],
        validateOnChange: true,
    });
    const handleSubmit = async (e) => {
        e.preventDefault();
        // Validate all fields
        const emailValid = await emailField.validate();
        const amountValid = await amountField.validate();
        if (emailValid && amountValid) {
            console.log('Form data:', {
                email: emailField.value,
                amount: amountField.value,
            });
        }
    };
    return (_jsxs("form", { onSubmit: handleSubmit, children: [_jsx("h3", { children: "Form Field Example" }), _jsxs("div", { children: [_jsx("label", { children: "Email:" }), _jsx("input", { type: "email", value: emailField.value, onChange: emailField.handleChange, onBlur: emailField.handleBlur, style: { borderColor: emailField.error ? 'red' : 'gray' } }), emailField.error && emailField.touched && (_jsx("div", { style: { color: 'red', fontSize: '12px' }, children: emailField.error }))] }), _jsx(EnhancedFormField, { name: "amount", label: "Trade Amount", type: "number", required: true, validationRules: [
                    validationRules.required('Amount is required'),
                    validationRules.min(0.01, 'Amount must be greater than 0'),
                ], placeholder: "Enter trade amount", helpText: "Enter the trade amount in USD" }), _jsx("button", { type: "submit", children: "Submit" })] }));
};
// ============================================================================
// EXAMPLE 3: useDataSection Hook
// ============================================================================
/**
 * Example: Using useDataSection for consistent data loading patterns
 *
 * BEFORE: Manual data fetching, loading, and error states in every component
 * AFTER: Centralized data section logic with automatic refresh
 */
export const DataSectionExample = () => {
    const tradesSection = useDataSection({
        fetchData: async () => {
            const response = await fetch('/api/trades');
            if (!response.ok)
                throw new Error('Failed to fetch trades');
            return response.json();
        },
        fetchOnMount: true,
        refreshInterval: 30000, // Refresh every 30 seconds
        isEmpty: (trades) => !trades || trades.length === 0,
    });
    if (tradesSection.isLoading) {
        return _jsx(LoadingSpinner, { size: "lg" });
    }
    if (tradesSection.error) {
        return (_jsxs("div", { children: [_jsxs("div", { style: { color: 'red' }, children: ["Error: ", tradesSection.error] }), _jsx("button", { onClick: tradesSection.refresh, children: "Retry" })] }));
    }
    if (tradesSection.isEmpty) {
        return _jsx("div", { children: "No trades found" });
    }
    return (_jsxs("div", { children: [_jsx("h3", { children: "Data Section Example" }), _jsxs("div", { children: [_jsx("button", { onClick: tradesSection.refresh, children: "Refresh" }), _jsxs("span", { children: ["Last updated: ", tradesSection.lastFetched?.toLocaleTimeString()] })] }), _jsxs("div", { children: ["Found ", tradesSection.data?.length, " trades"] })] }));
};
// ============================================================================
// EXAMPLE 4: useProfitLossFormatting Hook
// ============================================================================
/**
 * Example: Using useProfitLossFormatting for consistent P&L display
 *
 * BEFORE: Duplicate formatting logic in every component
 * AFTER: Centralized formatting with consistent styling
 */
export const ProfitLossFormattingExample = () => {
    const { formatCurrency, formatPercent } = useDataFormatting();
    const profit = useProfitLossFormatting(1234.56, {
        currency: '$',
        showPositiveSign: true,
    });
    const loss = useProfitLossFormatting(-567.89, {
        currency: '$',
    });
    const neutral = useProfitLossFormatting(0, {
        currency: '$',
    });
    return (_jsxs("div", { children: [_jsx("h3", { children: "Profit/Loss Formatting Example" }), _jsxs("div", { style: { color: profit.isProfit ? 'green' : 'red' }, children: ["Profit: ", profit.formattedAmount] }), _jsxs("div", { style: { color: loss.isLoss ? 'red' : 'green' }, children: ["Loss: ", loss.formattedAmount] }), _jsxs("div", { style: { color: 'gray' }, children: ["Neutral: ", neutral.formattedAmount] }), _jsxs("div", { children: [_jsx("h4", { children: "Data Formatting Examples:" }), _jsxs("div", { children: ["Currency: ", formatCurrency(1234.56)] }), _jsxs("div", { children: ["Percentage: ", formatPercent(75.5)] }), _jsxs("div", { children: ["Date: ", new Date().toLocaleDateString()] })] })] }));
};
// ============================================================================
// EXAMPLE 5: Complete Component Using Multiple Hooks
// ============================================================================
/**
 * Example: Complete component using multiple shared hooks
 *
 * This shows how the hooks work together to create a clean, maintainable component
 */
export const CompleteExample = () => {
    const { formatCurrency } = useDataFormatting();
    const tradesData = useDataSection({
        fetchData: async () => {
            // Simulate API call
            return [
                { id: 1, symbol: 'EURUSD', profitLoss: 123.45, date: '2023-12-01' },
                { id: 2, symbol: 'GBPUSD', profitLoss: -67.89, date: '2023-12-02' },
                { id: 3, symbol: 'USDJPY', profitLoss: 234.56, date: '2023-12-03' },
            ];
        },
        fetchOnMount: true,
    });
    const columns = [
        { field: 'symbol', label: 'Symbol', sortable: true },
        { field: 'profitLoss', label: 'P&L', sortable: true },
        { field: 'date', label: 'Date', sortable: true },
    ];
    const renderCell = (value, row, column) => {
        if (column.field === 'profitLoss') {
            return (_jsx("span", { style: { color: value > 0 ? 'green' : 'red' }, children: formatCurrency(value, { showPositiveSign: true }) }));
        }
        return value;
    };
    if (tradesData.isLoading)
        return _jsx(LoadingSpinner, {});
    if (tradesData.error)
        return _jsxs("div", { children: ["Error: ", tradesData.error] });
    return (_jsxs("div", { children: [_jsx("h3", { children: "Complete Example: Trades Table" }), _jsx(SortableTable, { data: tradesData.data || [], columns: columns, renderCell: renderCell, defaultSort: { field: 'profitLoss', direction: 'desc' } })] }));
};
// ============================================================================
// MAIN EXAMPLE COMPONENT
// ============================================================================
export const SharedHooksExamples = () => {
    return (_jsxs("div", { style: { padding: '20px', maxWidth: '800px' }, children: [_jsx("h1", { children: "Shared Hooks Usage Examples" }), _jsx("p", { children: "These examples demonstrate the new shared hooks created in Phase 1 of the refactoring plan. Each hook provides consistent behavior and reduces boilerplate code." }), _jsx(LoadingStateExample, {}), _jsx("hr", {}), _jsx(FormFieldExample, {}), _jsx("hr", {}), _jsx(DataSectionExample, {}), _jsx("hr", {}), _jsx(ProfitLossFormattingExample, {}), _jsx("hr", {}), _jsx(CompleteExample, {})] }));
};
export default SharedHooksExamples;
//# sourceMappingURL=shared-hooks-usage.js.map