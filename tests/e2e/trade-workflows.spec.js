/**
 * Critical Trade Workflows E2E Tests
 *
 * Testing complete user journeys after core package removal
 * Focuses on IndexedDB storage and trade form functionality
 */
import { test, expect } from '@playwright/test';
test.describe('Trade Workflows After Core Package Removal', () => {
    test.beforeEach(async ({ page }) => {
        // Navigate to the dashboard
        await page.goto('http://localhost:5173');
        // Wait for the application to load
        await page.waitForLoadState('networkidle');
        // Clear IndexedDB to start with clean state
        await page.evaluate(() => {
            return new Promise((resolve) => {
                const deleteReq = indexedDB.deleteDatabase('adhd-trading-dashboard');
                deleteReq.onsuccess = () => resolve(true);
                deleteReq.onerror = () => resolve(false);
            });
        });
    });
    test('Complete trade creation workflow', async ({ page }) => {
        // Navigate to trade journal
        await page.click('text=Trade Journal');
        await page.waitForURL('**/journal');
        // Click "Add New Trade" button
        await page.click('text=Add New Trade');
        await page.waitForURL('**/journal/new');
        // Fill out Basic Info tab
        await page.fill('[data-testid="date-input"]', '2024-01-15');
        await page.selectOption('[data-testid="market-select"]', 'MNQ');
        await page.selectOption('[data-testid="direction-select"]', 'Long');
        await page.selectOption('[data-testid="session-select"]', 'NY Open');
        await page.selectOption('[data-testid="model-type-select"]', 'RD-Cont');
        // Fill out entry and exit details
        await page.fill('[data-testid="entry-price-input"]', '15000');
        await page.fill('[data-testid="exit-price-input"]', '15100');
        await page.fill('[data-testid="quantity-input"]', '1');
        await page.fill('[data-testid="profit-input"]', '500');
        await page.selectOption('[data-testid="result-select"]', 'win');
        // Fill out timing information
        await page.fill('[data-testid="entry-time-input"]', '09:30');
        await page.fill('[data-testid="exit-time-input"]', '10:15');
        // Add notes
        await page.fill('[data-testid="notes-textarea"]', 'Test trade created via E2E test');
        // Submit the form
        await page.click('[data-testid="submit-button"]');
        // Wait for success message
        await expect(page.locator('text=created successfully')).toBeVisible({ timeout: 5000 });
        // Verify navigation back to journal
        await page.waitForURL('**/journal', { timeout: 10000 });
        // Verify trade appears in the list
        await expect(page.locator('text=MNQ')).toBeVisible();
        await expect(page.locator('text=2024-01-15')).toBeVisible();
        await expect(page.locator('text=$500')).toBeVisible();
        // Verify data persists after page refresh
        await page.reload();
        await page.waitForLoadState('networkidle');
        await expect(page.locator('text=MNQ')).toBeVisible();
        await expect(page.locator('text=2024-01-15')).toBeVisible();
    });
    test('Complete trade editing workflow', async ({ page }) => {
        // First create a trade to edit
        await page.goto('http://localhost:5173/journal/new');
        // Fill out basic trade information
        await page.fill('[data-testid="date-input"]', '2024-01-16');
        await page.selectOption('[data-testid="market-select"]', 'MNQ');
        await page.selectOption('[data-testid="direction-select"]', 'Short');
        await page.selectOption('[data-testid="session-select"]', 'Lunch Macro');
        await page.selectOption('[data-testid="model-type-select"]', 'FVG-RD');
        await page.fill('[data-testid="entry-price-input"]', '15200');
        await page.fill('[data-testid="exit-price-input"]', '15150');
        await page.fill('[data-testid="quantity-input"]', '2');
        await page.fill('[data-testid="profit-input"]', '1000');
        await page.selectOption('[data-testid="result-select"]', 'win');
        await page.fill('[data-testid="notes-textarea"]', 'Original trade for editing test');
        // Submit the trade
        await page.click('[data-testid="submit-button"]');
        await expect(page.locator('text=created successfully')).toBeVisible();
        await page.waitForURL('**/journal');
        // Now edit the trade
        await page.click('[data-testid="edit-trade-button"]');
        await page.waitForURL('**/journal/edit/*');
        // Verify form is populated with existing data
        await expect(page.locator('[data-testid="date-input"]')).toHaveValue('2024-01-16');
        await expect(page.locator('[data-testid="entry-price-input"]')).toHaveValue('15200');
        // Modify some fields
        await page.fill('[data-testid="exit-price-input"]', '15175'); // Better exit
        await page.fill('[data-testid="profit-input"]', '1250'); // Updated profit
        await page.fill('[data-testid="notes-textarea"]', 'Updated trade via E2E edit test');
        // Save changes
        await page.click('[data-testid="submit-button"]');
        // Wait for success message
        await expect(page.locator('text=updated successfully')).toBeVisible();
        await page.waitForURL('**/journal');
        // Verify updated data in trade list
        await expect(page.locator('text=$1,250')).toBeVisible();
        await expect(page.locator('text=Updated trade via E2E edit test')).toBeVisible();
        // Verify changes persist after refresh
        await page.reload();
        await page.waitForLoadState('networkidle');
        await expect(page.locator('text=$1,250')).toBeVisible();
    });
    test('Navigation between dashboard features', async ({ page }) => {
        // Start at dashboard
        await page.goto('http://localhost:5173');
        await expect(page.locator('h1')).toContainText('Trading Dashboard');
        // Navigate to Trade Journal
        await page.click('text=Trade Journal');
        await page.waitForURL('**/journal');
        await expect(page.locator('h1')).toContainText('Trade Journal');
        // Navigate to Trade Analysis
        await page.click('text=Trade Analysis');
        await page.waitForURL('**/analysis');
        await expect(page.locator('h1')).toContainText('Trade Analysis');
        // Navigate to Performance Dashboard
        await page.click('text=Performance');
        await page.waitForURL('**/performance');
        await expect(page.locator('h1')).toContainText('Performance Dashboard');
        // Navigate to Daily Guide
        await page.click('text=Daily Guide');
        await page.waitForURL('**/daily-guide');
        await expect(page.locator('h1')).toContainText('Daily Guide');
        // Verify no console errors during navigation
        const consoleErrors = [];
        page.on('console', (msg) => {
            if (msg.type() === 'error') {
                consoleErrors.push(msg.text());
            }
        });
        // Navigate through features again to trigger any lazy loading
        await page.click('text=Trade Journal');
        await page.waitForURL('**/journal');
        await page.click('text=Trade Analysis');
        await page.waitForURL('**/analysis');
        // Check for console errors
        expect(consoleErrors.filter(error => !error.includes('favicon') &&
            !error.includes('404'))).toHaveLength(0);
    });
    test('IndexedDB operations work correctly', async ({ page }) => {
        // Navigate to trade journal
        await page.goto('http://localhost:5173/journal/new');
        // Create multiple trades to test database operations
        const trades = [
            { date: '2024-01-15', market: 'MNQ', direction: 'Long', profit: '500' },
            { date: '2024-01-16', market: 'ES', direction: 'Short', profit: '750' },
            { date: '2024-01-17', market: 'MNQ', direction: 'Long', profit: '300' },
        ];
        for (const trade of trades) {
            // Fill out trade form
            await page.fill('[data-testid="date-input"]', trade.date);
            await page.selectOption('[data-testid="market-select"]', trade.market);
            await page.selectOption('[data-testid="direction-select"]', trade.direction);
            await page.selectOption('[data-testid="session-select"]', 'NY Open');
            await page.selectOption('[data-testid="model-type-select"]', 'RD-Cont');
            await page.fill('[data-testid="entry-price-input"]', '15000');
            await page.fill('[data-testid="exit-price-input"]', '15100');
            await page.fill('[data-testid="quantity-input"]', '1');
            await page.fill('[data-testid="profit-input"]', trade.profit);
            await page.selectOption('[data-testid="result-select"]', 'win');
            // Submit trade
            await page.click('[data-testid="submit-button"]');
            await expect(page.locator('text=created successfully')).toBeVisible();
            await page.waitForURL('**/journal');
            // Go back to create next trade
            if (trade !== trades[trades.length - 1]) {
                await page.click('text=Add New Trade');
                await page.waitForURL('**/journal/new');
            }
        }
        // Verify all trades are stored and displayed
        await page.goto('http://localhost:5173/journal');
        await page.waitForLoadState('networkidle');
        for (const trade of trades) {
            await expect(page.locator(`text=${trade.date}`)).toBeVisible();
            await expect(page.locator(`text=${trade.market}`)).toBeVisible();
            await expect(page.locator(`text=$${trade.profit}`)).toBeVisible();
        }
        // Test data persistence across browser sessions
        await page.context().storageState({ path: 'test-storage-state.json' });
        // Simulate browser restart by creating new context
        const newContext = await page.context().browser()?.newContext({
            storageState: 'test-storage-state.json'
        });
        if (newContext) {
            const newPage = await newContext.newPage();
            await newPage.goto('http://localhost:5173/journal');
            await newPage.waitForLoadState('networkidle');
            // Verify data still exists
            for (const trade of trades) {
                await expect(newPage.locator(`text=${trade.date}`)).toBeVisible();
            }
            await newContext.close();
        }
    });
    test('Form validation works correctly', async ({ page }) => {
        // Navigate to trade form
        await page.goto('http://localhost:5173/journal/new');
        // Try to submit empty form
        await page.click('[data-testid="submit-button"]');
        // Should show validation errors
        await expect(page.locator('text=required')).toBeVisible();
        // Fill only some required fields
        await page.fill('[data-testid="date-input"]', '2024-01-15');
        await page.selectOption('[data-testid="market-select"]', 'MNQ');
        // Try to submit again
        await page.click('[data-testid="submit-button"]');
        // Should still show validation errors for missing fields
        await expect(page.locator('text=required')).toBeVisible();
        // Fill all required fields
        await page.selectOption('[data-testid="direction-select"]', 'Long');
        await page.selectOption('[data-testid="session-select"]', 'NY Open');
        await page.selectOption('[data-testid="model-type-select"]', 'RD-Cont');
        await page.fill('[data-testid="entry-price-input"]', '15000');
        await page.fill('[data-testid="exit-price-input"]', '15100');
        await page.fill('[data-testid="quantity-input"]', '1');
        await page.fill('[data-testid="profit-input"]', '500');
        await page.selectOption('[data-testid="result-select"]', 'win');
        // Now submission should work
        await page.click('[data-testid="submit-button"]');
        await expect(page.locator('text=created successfully')).toBeVisible();
    });
    test('Performance metrics calculate correctly', async ({ page }) => {
        // Create some test trades with known metrics
        await page.goto('http://localhost:5173/journal/new');
        // Create winning trade
        await page.fill('[data-testid="date-input"]', '2024-01-15');
        await page.selectOption('[data-testid="market-select"]', 'MNQ');
        await page.selectOption('[data-testid="direction-select"]', 'Long');
        await page.selectOption('[data-testid="session-select"]', 'NY Open');
        await page.selectOption('[data-testid="model-type-select"]', 'RD-Cont');
        await page.fill('[data-testid="entry-price-input"]', '15000');
        await page.fill('[data-testid="exit-price-input"]', '15100');
        await page.fill('[data-testid="quantity-input"]', '1');
        await page.fill('[data-testid="profit-input"]', '500');
        await page.selectOption('[data-testid="result-select"]', 'win');
        await page.click('[data-testid="submit-button"]');
        await expect(page.locator('text=created successfully')).toBeVisible();
        // Create losing trade
        await page.click('text=Add New Trade');
        await page.fill('[data-testid="date-input"]', '2024-01-16');
        await page.selectOption('[data-testid="market-select"]', 'MNQ');
        await page.selectOption('[data-testid="direction-select"]', 'Short');
        await page.selectOption('[data-testid="session-select"]', 'Lunch Macro');
        await page.selectOption('[data-testid="model-type-select"]', 'FVG-RD');
        await page.fill('[data-testid="entry-price-input"]', '15200');
        await page.fill('[data-testid="exit-price-input"]', '15250');
        await page.fill('[data-testid="quantity-input"]', '1');
        await page.fill('[data-testid="profit-input"]', '-250');
        await page.selectOption('[data-testid="result-select"]', 'loss');
        await page.click('[data-testid="submit-button"]');
        await expect(page.locator('text=created successfully')).toBeVisible();
        // Navigate to performance dashboard
        await page.click('text=Performance');
        await page.waitForURL('**/performance');
        await page.waitForLoadState('networkidle');
        // Verify performance metrics are calculated
        await expect(page.locator('text=Total Trades')).toBeVisible();
        await expect(page.locator('text=2')).toBeVisible(); // Should show 2 trades
        await expect(page.locator('text=Win Rate')).toBeVisible();
        await expect(page.locator('text=50%')).toBeVisible(); // 1 win out of 2 trades
        await expect(page.locator('text=Total P&L')).toBeVisible();
        await expect(page.locator('text=$250')).toBeVisible(); // 500 - 250 = 250
    });
});
//# sourceMappingURL=trade-workflows.spec.js.map