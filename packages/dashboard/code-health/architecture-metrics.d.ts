#!/usr/bin/env node
/**
 * Calculate component metrics
 */
export function analyzeComponent(filePath: any, content: any): {
    filePath: string;
    metrics: {
        lines: any;
        imports: any;
        complexity: any;
        responsibilities: number;
    };
    scores: {
        lines: number;
        imports: number;
        complexity: number;
        responsibilities: number;
    };
    overallHealth: number;
    detectedPatterns: string[];
    recommendations: {
        type: string;
        message: string;
        action: string;
        priority: string;
    }[];
};
/**
 * Scan directory for components
 */
export function scanDirectory(dir: any): any[];
/**
 * Generate summary report
 */
export function generateSummary(analyses: any): {
    timestamp: string;
    summary: {
        totalComponents: any;
        averageHealth: number;
        healthDistribution: {
            excellent: any;
            good: any;
            warning: any;
            critical: any;
        };
        patternUsage: {};
    };
    topIssues: any;
    recommendations: {
        immediate: any;
        monitor: any;
    };
};
//# sourceMappingURL=architecture-metrics.d.ts.map