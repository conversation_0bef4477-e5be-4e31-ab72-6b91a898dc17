import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { VitePWA } from 'vite-plugin-pwa';
export default defineConfig({
    // Base path set to '/' for absolute asset references (works better with <PERSON><PERSON>er<PERSON><PERSON><PERSON>)
    base: '/',
    // Explicitly set the public directory
    publicDir: 'public',
    // Configure the build to copy assets from public directory
    plugins: [
        react({
            // Simplified babel configuration
            babel: {
                plugins: [
                    [
                        'babel-plugin-styled-components',
                        {
                            displayName: true,
                            fileName: false,
                            pure: true,
                        },
                    ],
                ],
            },
            // Fix for the preamble detection issue
            jsxRuntime: 'automatic',
            jsxImportSource: 'react',
            fastRefresh: true,
        }),
        VitePWA({
            registerType: 'autoUpdate',
            includeAssets: ['favicon.ico', 'logo192.png', 'logo512.png'],
            injectRegister: 'auto',
            devOptions: {
                enabled: true,
                navigateFallbackAllowlist: [/^index.html$/],
            },
            workbox: {
                globPatterns: ['**/*.{js,css,html,ico,png,svg}'],
                maximumFileSizeToCacheInBytes: 3000000,
            },
            manifest: {
                name: 'ADHD Trading Dashboard',
                short_name: 'ADHD Trading',
                theme_color: '#e10600',
                background_color: '#1a1f2c',
                display: 'standalone',
                start_url: '/',
                scope: '/',
                icons: [
                    {
                        src: '/favicon.ico',
                        sizes: '64x64 32x32 24x24 16x16',
                        type: 'image/x-icon',
                    },
                    {
                        src: '/logo192.png',
                        type: 'image/png',
                        sizes: '192x192',
                        purpose: 'any maskable',
                    },
                    {
                        src: '/logo512.png',
                        type: 'image/png',
                        sizes: '512x512',
                        purpose: 'any maskable',
                    },
                ],
            },
        }),
    ],
    resolve: {
        alias: {
            '@api': resolve(__dirname, 'src/api'),
            '@components': resolve(__dirname, 'src/components'),
            '@context': resolve(__dirname, 'src/context'),
            '@hooks': resolve(__dirname, 'src/hooks'),
            '@layouts': resolve(__dirname, 'src/layouts'),
            '@pages': resolve(__dirname, 'src/pages'),
            '@routes': resolve(__dirname, 'src/routes'),
            '@services': resolve(__dirname, 'src/services'),
            '@styles': resolve(__dirname, 'src/styles'),
            '@theme': resolve(__dirname, 'src/theme'),
            '@utils': resolve(__dirname, 'src/utils'),
            '@adhd-trading-dashboard/shared': resolve(__dirname, '../shared/src'),
        },
        preserveSymlinks: true,
    },
    server: {
        port: 3000,
        open: true,
    },
    preview: {
        port: 5173,
    },
    build: {
        outDir: 'dist',
        sourcemap: true,
        // Ensure assets are properly referenced with relative paths
        assetsDir: 'assets',
        // Don't inline assets, keep them as separate files
        assetsInlineLimit: 0,
        // Explicitly setting the input to include index.html and simple.html
        rollupOptions: {
            input: {
                main: resolve(__dirname, 'index.html'),
                simple: resolve(__dirname, 'simple.html'),
            },
            output: {
                manualChunks: {
                    react: ['react', 'react-dom'],
                    'styled-components': ['styled-components'],
                    recharts: ['recharts'],
                    router: ['react-router-dom', 'react-router'],
                },
                // Ensure assets have consistent paths
                assetFileNames: (assetInfo) => {
                    // Keep original file names and paths for assets
                    const info = assetInfo.name || '';
                    const extType = info.split('.').at(1) || '';
                    // Special handling for PWA assets
                    if (info.includes('favicon.ico') ||
                        info.includes('logo192.png') ||
                        info.includes('logo512.png') ||
                        info.includes('manifest.json') ||
                        info.includes('manifest.webmanifest')) {
                        // Keep these files at the root level for PWA support
                        return `[name][extname]`;
                    }
                    // Other image assets
                    if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(extType)) {
                        return `assets/images/[name][extname]`;
                    }
                    return `assets/[name][extname]`;
                },
            },
        },
    },
});
//# sourceMappingURL=vite.config.js.map