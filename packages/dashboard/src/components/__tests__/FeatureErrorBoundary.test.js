import { jsx as _jsx, Fragment as _Fragment } from "react/jsx-runtime";
import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeAll, afterAll } from 'vitest';
import FeatureErrorBoundary from '../FeatureErrorBoundary';
// Mock the ErrorBoundary component from shared package
vi.mock('@adhd-trading-dashboard/shared', async () => {
    const actual = await vi.importActual('@adhd-trading-dashboard/shared');
    return {
        ...actual,
        ErrorBoundary: ({ children, fallback, onError }) => {
            // For testing, we'll render the fallback directly if the test case includes an error
            const error = new Error('Test error');
            const resetError = vi.fn();
            if (window.__TEST_WITH_ERROR__) {
                // Call onError if provided
                if (onError) {
                    onError(error);
                }
                // Render fallback
                if (typeof fallback === 'function') {
                    return fallback({ error, resetError });
                }
                return fallback || _jsx("div", { children: "Error fallback" });
            }
            // Otherwise render children
            return children;
        },
        ThemeProvider: ({ children }) => _jsx(_Fragment, { children: children }),
    };
});
// Mock console.error to avoid test output noise
const originalConsoleError = console.error;
beforeAll(() => {
    console.error = vi.fn();
    // @ts-ignore - Add a global flag to simulate error state
    window.__TEST_WITH_ERROR__ = false;
});
afterAll(() => {
    console.error = originalConsoleError;
    // @ts-ignore - Clean up
    delete window.__TEST_WITH_ERROR__;
});
describe('FeatureErrorBoundary', () => {
    it('renders children when there is no error', () => {
        // @ts-ignore - Set the global flag to false for this test
        window.__TEST_WITH_ERROR__ = false;
        render(_jsx(FeatureErrorBoundary, { featureName: "Test Feature", children: _jsx("div", { "data-testid": "child", children: "Child content" }) }));
        expect(screen.getByTestId('child')).toBeInTheDocument();
        expect(screen.getByText('Child content')).toBeInTheDocument();
    });
    it('renders the error UI when an error occurs', () => {
        // @ts-ignore - Set the global flag to true for this test
        window.__TEST_WITH_ERROR__ = true;
        render(_jsx(FeatureErrorBoundary, { featureName: "Test Feature", children: _jsx("div", { children: "This content should not be visible" }) }));
        // The FeatureErrorFallback component should be rendered
        expect(screen.getByRole('heading', { name: /Error in Test Feature/i })).toBeInTheDocument();
        expect(screen.getByText(/We encountered a problem while loading this feature/i)).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /Try Again/i })).toBeInTheDocument();
    });
    it('calls onError when an error occurs', () => {
        // @ts-ignore - Set the global flag to true for this test
        window.__TEST_WITH_ERROR__ = true;
        const onError = vi.fn();
        render(_jsx(FeatureErrorBoundary, { featureName: "Test Feature", onError: onError, children: _jsx("div", { children: "This content should not be visible" }) }));
        expect(onError).toHaveBeenCalledTimes(1);
        expect(onError).toHaveBeenCalledWith(expect.objectContaining({ message: 'Test error' }));
    });
    it('calls onSkip when skip button is clicked', () => {
        // @ts-ignore - Set the global flag to true for this test
        window.__TEST_WITH_ERROR__ = true;
        const onSkip = vi.fn();
        render(_jsx(FeatureErrorBoundary, { featureName: "Test Feature", onSkip: onSkip, children: _jsx("div", { children: "This content should not be visible" }) }));
        const skipButton = screen.getByRole('button', { name: /Skip This Feature/i });
        fireEvent.click(skipButton);
        expect(onSkip).toHaveBeenCalledTimes(1);
    });
});
//# sourceMappingURL=FeatureErrorBoundary.test.js.map