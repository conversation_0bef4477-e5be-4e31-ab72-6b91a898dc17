/**
 * Trade Submission Hook Tests
 *
 * Testing trade form submission logic after core package removal
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useTradeSubmission } from '../hooks/useTradeSubmission';
import { tradeStorageService } from '@adhd-trading-dashboard/shared/services';
// Mock the trade storage service
vi.mock('@adhd-trading-dashboard/shared/services', () => ({
    tradeStorageService: {
        saveTradeWithDetails: vi.fn(),
        updateTradeWithDetails: vi.fn(),
    },
}));
// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', () => ({
    useNavigate: () => mockNavigate,
}));
// Mock pattern quality calculations
vi.mock('../constants/patternQuality', () => ({
    calculateTotalScore: vi.fn(() => 85),
    convertScoreToRating: vi.fn(() => 4),
}));
describe('useTradeSubmission', () => {
    const mockSetActiveTab = vi.fn();
    const mockSetError = vi.fn();
    const mockSetSuccess = vi.fn();
    const mockValidateBasicInfoTab = vi.fn();
    const mockValidateCurrentTab = vi.fn();
    const mockFormValues = {
        date: '2024-01-15',
        symbol: 'MNQ',
        direction: 'long',
        session: 'NY Open',
        modelType: 'RD-Cont',
        market: 'MNQ',
        entryPrice: '15000',
        exitPrice: '15100',
        quantity: '1',
        profit: '500',
        rMultiple: '2.5',
        result: 'win',
        patternQuality: '4',
        entryTime: '09:30',
        exitTime: '10:15',
        notes: 'Test trade',
        // Pattern quality fields
        patternQualityClarity: 'excellent',
        patternQualityConfluence: 'high',
        patternQualityContext: 'trending',
        patternQualityRisk: 'low',
        patternQualityReward: 'high',
        patternQualityTimeframe: 'aligned',
        patternQualityVolume: 'strong',
        patternQualityNotes: 'Perfect setup',
        // DOL analysis fields
        dolType: 'SSL',
        dolStrength: 'strong',
        dolReaction: 'immediate',
        dolContext: ['trend', 'structure'],
        dolPriceAction: 'clean',
        dolVolumeProfile: 'high',
        dolTimeOfDay: 'open',
        dolMarketStructure: 'trending',
        dolEffectiveness: '9',
        dolNotes: 'Excellent DOL reaction',
    };
    const mockTradeData = {
        id: 1,
        date: '2024-01-15',
        symbol: 'MNQ',
        direction: 'Long',
        session: 'NY Open',
        modelType: 'RD-Cont',
        market: 'MNQ',
        entryPrice: 15000,
        exitPrice: 15100,
        quantity: 1,
        profit: 500,
        rMultiple: 2.5,
        result: 'Win',
        patternQuality: 4,
        entryTime: '09:30',
        exitTime: '10:15',
        notes: 'Test trade',
    };
    beforeEach(() => {
        vi.clearAllMocks();
        mockValidateBasicInfoTab.mockReturnValue(true);
        mockValidateCurrentTab.mockReturnValue(true);
    });
    afterEach(() => {
        vi.restoreAllMocks();
    });
    describe('Create Mode', () => {
        it('should save a new trade successfully', async () => {
            const mockSaveTradeId = 123;
            tradeStorageService.saveTradeWithDetails.mockResolvedValue(mockSaveTradeId);
            const { result } = renderHook(() => useTradeSubmission(mockFormValues, false, // isEditMode
            true, // isNewTrade
            null, // tradeData
            mockValidateBasicInfoTab, mockValidateCurrentTab, 'basic', // activeTab
            mockSetActiveTab, mockSetError, mockSetSuccess));
            const mockEvent = { preventDefault: vi.fn() };
            await act(async () => {
                await result.current.handleSubmit(mockEvent);
            });
            expect(mockEvent.preventDefault).toHaveBeenCalled();
            expect(mockValidateBasicInfoTab).toHaveBeenCalled();
            expect(tradeStorageService.saveTradeWithDetails).toHaveBeenCalledWith(expect.objectContaining({
                trade: expect.objectContaining({
                    date: '2024-01-15',
                    market: 'MNQ',
                    direction: 'Long',
                    session: 'NY Open',
                    model_type: 'RD-Cont',
                    entry_price: 15000,
                    exit_price: 15100,
                    achieved_pl: 500,
                    r_multiple: 2.5,
                    win_loss: 'Win',
                    pattern_quality_rating: 4,
                }),
                fvg_details: expect.any(Object),
                setup: expect.any(Object),
                analysis: expect.any(Object),
            }));
            expect(mockSetSuccess).toHaveBeenCalledWith('New trade for MNQ on 2024-01-15 created successfully!');
            expect(mockNavigate).toHaveBeenCalledWith('/journal');
        });
        it('should handle validation errors', async () => {
            mockValidateBasicInfoTab.mockReturnValue(false);
            const { result } = renderHook(() => useTradeSubmission(mockFormValues, false, // isEditMode
            true, // isNewTrade
            null, // tradeData
            mockValidateBasicInfoTab, mockValidateCurrentTab, 'basic', // activeTab
            mockSetActiveTab, mockSetError, mockSetSuccess));
            const mockEvent = { preventDefault: vi.fn() };
            await act(async () => {
                await result.current.handleSubmit(mockEvent);
            });
            expect(mockSetError).toHaveBeenCalledWith('Please complete the required fields in the Basic Info tab.');
            expect(mockSetActiveTab).toHaveBeenCalledWith('basic');
            expect(tradeStorageService.saveTradeWithDetails).not.toHaveBeenCalled();
        });
        it('should handle save errors gracefully', async () => {
            tradeStorageService.saveTradeWithDetails.mockRejectedValue(new Error('Database error'));
            const { result } = renderHook(() => useTradeSubmission(mockFormValues, false, // isEditMode
            true, // isNewTrade
            null, // tradeData
            mockValidateBasicInfoTab, mockValidateCurrentTab, 'basic', // activeTab
            mockSetActiveTab, mockSetError, mockSetSuccess));
            const mockEvent = { preventDefault: vi.fn() };
            await act(async () => {
                await result.current.handleSubmit(mockEvent);
            });
            expect(mockSetError).toHaveBeenCalledWith('Failed to save trade. Please try again.');
            expect(result.current.isSubmitting).toBe(false);
        });
    });
    describe('Edit Mode', () => {
        it('should update an existing trade successfully', async () => {
            tradeStorageService.updateTradeWithDetails.mockResolvedValue(undefined);
            const { result } = renderHook(() => useTradeSubmission(mockFormValues, true, // isEditMode
            false, // isNewTrade
            mockTradeData, // tradeData
            mockValidateBasicInfoTab, mockValidateCurrentTab, 'basic', // activeTab
            mockSetActiveTab, mockSetError, mockSetSuccess));
            const mockEvent = { preventDefault: vi.fn() };
            await act(async () => {
                await result.current.handleSubmit(mockEvent);
            });
            expect(tradeStorageService.updateTradeWithDetails).toHaveBeenCalledWith(1, // trade ID
            expect.objectContaining({
                trade: expect.objectContaining({
                    date: '2024-01-15',
                    market: 'MNQ',
                    direction: 'Long',
                    achieved_pl: 500,
                }),
            }));
            expect(mockSetSuccess).toHaveBeenCalledWith('Trade for MNQ on 2024-01-15 updated successfully!');
        });
        it('should handle string trade IDs correctly', async () => {
            const tradeDataWithStringId = { ...mockTradeData, id: '42' };
            tradeStorageService.updateTradeWithDetails.mockResolvedValue(undefined);
            const { result } = renderHook(() => useTradeSubmission(mockFormValues, true, // isEditMode
            false, // isNewTrade
            tradeDataWithStringId, // tradeData with string ID
            mockValidateBasicInfoTab, mockValidateCurrentTab, 'basic', // activeTab
            mockSetActiveTab, mockSetError, mockSetSuccess));
            const mockEvent = { preventDefault: vi.fn() };
            await act(async () => {
                await result.current.handleSubmit(mockEvent);
            });
            expect(tradeStorageService.updateTradeWithDetails).toHaveBeenCalledWith(42, // converted to number
            expect.any(Object));
        });
    });
    describe('Data Transformation', () => {
        it('should correctly transform form values to trade record format', async () => {
            tradeStorageService.saveTradeWithDetails.mockResolvedValue(1);
            const { result } = renderHook(() => useTradeSubmission(mockFormValues, false, // isEditMode
            true, // isNewTrade
            null, // tradeData
            mockValidateBasicInfoTab, mockValidateCurrentTab, 'basic', // activeTab
            mockSetActiveTab, mockSetError, mockSetSuccess));
            const mockEvent = { preventDefault: vi.fn() };
            await act(async () => {
                await result.current.handleSubmit(mockEvent);
            });
            const saveCall = tradeStorageService.saveTradeWithDetails.mock.calls[0][0];
            expect(saveCall.trade).toEqual(expect.objectContaining({
                direction: 'Long', // transformed from 'long'
                win_loss: 'Win', // transformed from 'win'
                entry_price: 15000, // parsed from string
                exit_price: 15100, // parsed from string
                achieved_pl: 500, // parsed from string
                r_multiple: 2.5, // parsed from string
                no_of_contracts: 1, // parsed from string
            }));
        });
        it('should include pattern quality data when all criteria are filled', async () => {
            tradeStorageService.saveTradeWithDetails.mockResolvedValue(1);
            const { result } = renderHook(() => useTradeSubmission(mockFormValues, false, // isEditMode
            true, // isNewTrade
            null, // tradeData
            mockValidateBasicInfoTab, mockValidateCurrentTab, 'basic', // activeTab
            mockSetActiveTab, mockSetError, mockSetSuccess));
            const mockEvent = { preventDefault: vi.fn() };
            await act(async () => {
                await result.current.handleSubmit(mockEvent);
            });
            // Pattern quality calculations should be imported and used
            const { calculateTotalScore, convertScoreToRating } = await import('../constants/patternQuality');
            expect(calculateTotalScore).toHaveBeenCalled();
            expect(convertScoreToRating).toHaveBeenCalled();
        });
        it('should include DOL analysis data when DOL type is selected', async () => {
            tradeStorageService.saveTradeWithDetails.mockResolvedValue(1);
            const { result } = renderHook(() => useTradeSubmission(mockFormValues, false, // isEditMode
            true, // isNewTrade
            null, // tradeData
            mockValidateBasicInfoTab, mockValidateCurrentTab, 'basic', // activeTab
            mockSetActiveTab, mockSetError, mockSetSuccess));
            const mockEvent = { preventDefault: vi.fn() };
            await act(async () => {
                await result.current.handleSubmit(mockEvent);
            });
            const saveCall = tradeStorageService.saveTradeWithDetails.mock.calls[0][0];
            expect(saveCall.analysis).toEqual(expect.objectContaining({
                dol_notes: 'Excellent DOL reaction',
                path_quality: 'clean',
                clustering: 'high',
            }));
        });
    });
    describe('Loading States', () => {
        it('should manage isSubmitting state correctly', async () => {
            let resolvePromise;
            const savePromise = new Promise((resolve) => {
                resolvePromise = resolve;
            });
            tradeStorageService.saveTradeWithDetails.mockReturnValue(savePromise);
            const { result } = renderHook(() => useTradeSubmission(mockFormValues, false, // isEditMode
            true, // isNewTrade
            null, // tradeData
            mockValidateBasicInfoTab, mockValidateCurrentTab, 'basic', // activeTab
            mockSetActiveTab, mockSetError, mockSetSuccess));
            expect(result.current.isSubmitting).toBe(false);
            const mockEvent = { preventDefault: vi.fn() };
            act(() => {
                result.current.handleSubmit(mockEvent);
            });
            expect(result.current.isSubmitting).toBe(true);
            await act(async () => {
                resolvePromise(1);
            });
            expect(result.current.isSubmitting).toBe(false);
        });
    });
});
//# sourceMappingURL=useTradeSubmission.test.js.map