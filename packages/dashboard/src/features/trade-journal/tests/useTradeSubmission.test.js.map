{"version": 3, "file": "useTradeSubmission.test.js", "sourceRoot": "", "sources": ["useTradeSubmission.test.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,EAAE,EAAE,MAAM,QAAQ,CAAC;AACzE,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,MAAM,wBAAwB,CAAC;AACzD,OAAO,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAC;AAEjE,OAAO,EAAE,mBAAmB,EAAE,MAAM,yCAAyC,CAAC;AAE9E,iCAAiC;AACjC,EAAE,CAAC,IAAI,CAAC,yCAAyC,EAAE,GAAG,EAAE,CAAC,CAAC;IACxD,mBAAmB,EAAE;QACnB,oBAAoB,EAAE,EAAE,CAAC,EAAE,EAAE;QAC7B,sBAAsB,EAAE,EAAE,CAAC,EAAE,EAAE;KAChC;CACF,CAAC,CAAC,CAAC;AAEJ,wBAAwB;AACxB,MAAM,YAAY,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;AAC7B,EAAE,CAAC,IAAI,CAAC,kBAAkB,EAAE,GAAG,EAAE,CAAC,CAAC;IACjC,WAAW,EAAE,GAAG,EAAE,CAAC,YAAY;CAChC,CAAC,CAAC,CAAC;AAEJ,oCAAoC;AACpC,EAAE,CAAC,IAAI,CAAC,6BAA6B,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5C,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;IACpC,oBAAoB,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;CACrC,CAAC,CAAC,CAAC;AAEJ,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;IAClC,MAAM,gBAAgB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;IACjC,MAAM,YAAY,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;IAC7B,MAAM,cAAc,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;IAC/B,MAAM,wBAAwB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;IACzC,MAAM,sBAAsB,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;IAEvC,MAAM,cAAc,GAAoB;QACtC,IAAI,EAAE,YAAY;QAClB,MAAM,EAAE,KAAK;QACb,SAAS,EAAE,MAAM;QACjB,OAAO,EAAE,SAAS;QAClB,SAAS,EAAE,SAAS;QACpB,MAAM,EAAE,KAAK;QACb,UAAU,EAAE,OAAO;QACnB,SAAS,EAAE,OAAO;QAClB,QAAQ,EAAE,GAAG;QACb,MAAM,EAAE,KAAK;QACb,SAAS,EAAE,KAAK;QAChB,MAAM,EAAE,KAAK;QACb,cAAc,EAAE,GAAG;QACnB,SAAS,EAAE,OAAO;QAClB,QAAQ,EAAE,OAAO;QACjB,KAAK,EAAE,YAAY;QACnB,yBAAyB;QACzB,qBAAqB,EAAE,WAAW;QAClC,wBAAwB,EAAE,MAAM;QAChC,qBAAqB,EAAE,UAAU;QACjC,kBAAkB,EAAE,KAAK;QACzB,oBAAoB,EAAE,MAAM;QAC5B,uBAAuB,EAAE,SAAS;QAClC,oBAAoB,EAAE,QAAQ;QAC9B,mBAAmB,EAAE,eAAe;QACpC,sBAAsB;QACtB,OAAO,EAAE,KAAK;QACd,WAAW,EAAE,QAAQ;QACrB,WAAW,EAAE,WAAW;QACxB,UAAU,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC;QAClC,cAAc,EAAE,OAAO;QACvB,gBAAgB,EAAE,MAAM;QACxB,YAAY,EAAE,MAAM;QACpB,kBAAkB,EAAE,UAAU;QAC9B,gBAAgB,EAAE,GAAG;QACrB,QAAQ,EAAE,wBAAwB;KACnC,CAAC;IAEF,MAAM,aAAa,GAAU;QAC3B,EAAE,EAAE,CAAC;QACL,IAAI,EAAE,YAAY;QAClB,MAAM,EAAE,KAAK;QACb,SAAS,EAAE,MAAM;QACjB,OAAO,EAAE,SAAS;QAClB,SAAS,EAAE,SAAS;QACpB,MAAM,EAAE,KAAK;QACb,UAAU,EAAE,KAAK;QACjB,SAAS,EAAE,KAAK;QAChB,QAAQ,EAAE,CAAC;QACX,MAAM,EAAE,GAAG;QACX,SAAS,EAAE,GAAG;QACd,MAAM,EAAE,KAAK;QACb,cAAc,EAAE,CAAC;QACjB,SAAS,EAAE,OAAO;QAClB,QAAQ,EAAE,OAAO;QACjB,KAAK,EAAE,YAAY;KACpB,CAAC;IAEF,UAAU,CAAC,GAAG,EAAE;QACd,EAAE,CAAC,aAAa,EAAE,CAAC;QACnB,wBAAwB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC/C,sBAAsB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;IAC/C,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,GAAG,EAAE;QACb,EAAE,CAAC,eAAe,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,aAAa,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,eAAe,GAAG,GAAG,CAAC;YAC3B,mBAAmB,CAAC,oBAA4B,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;YAErF,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CACjC,kBAAkB,CAChB,cAAc,EACd,KAAK,EAAE,aAAa;YACpB,IAAI,EAAG,aAAa;YACpB,IAAI,EAAG,YAAY;YACnB,wBAAwB,EACxB,sBAAsB,EACtB,OAAO,EAAE,YAAY;YACrB,gBAAgB,EAChB,YAAY,EACZ,cAAc,CACf,CACF,CAAC;YAEF,MAAM,SAAS,GAAG,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,EAAS,CAAC;YAErD,MAAM,GAAG,CAAC,KAAK,IAAI,EAAE;gBACnB,MAAM,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACpD,MAAM,CAAC,wBAAwB,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACpD,MAAM,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,CAAC,oBAAoB,CACnE,MAAM,CAAC,gBAAgB,CAAC;gBACtB,KAAK,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC7B,IAAI,EAAE,YAAY;oBAClB,MAAM,EAAE,KAAK;oBACb,SAAS,EAAE,MAAM;oBACjB,OAAO,EAAE,SAAS;oBAClB,UAAU,EAAE,SAAS;oBACrB,WAAW,EAAE,KAAK;oBAClB,UAAU,EAAE,KAAK;oBACjB,WAAW,EAAE,GAAG;oBAChB,UAAU,EAAE,GAAG;oBACf,QAAQ,EAAE,KAAK;oBACf,sBAAsB,EAAE,CAAC;iBAC1B,CAAC;gBACF,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBAC/B,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;gBACzB,QAAQ,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;aAC7B,CAAC,CACH,CAAC;YACF,MAAM,CAAC,cAAc,CAAC,CAAC,oBAAoB,CACzC,uDAAuD,CACxD,CAAC;YACF,MAAM,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAC/C,wBAAwB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CACjC,kBAAkB,CAChB,cAAc,EACd,KAAK,EAAE,aAAa;YACpB,IAAI,EAAG,aAAa;YACpB,IAAI,EAAG,YAAY;YACnB,wBAAwB,EACxB,sBAAsB,EACtB,OAAO,EAAE,YAAY;YACrB,gBAAgB,EAChB,YAAY,EACZ,cAAc,CACf,CACF,CAAC;YAEF,MAAM,SAAS,GAAG,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,EAAS,CAAC;YAErD,MAAM,GAAG,CAAC,KAAK,IAAI,EAAE;gBACnB,MAAM,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,CAAC,oBAAoB,CACvC,4DAA4D,CAC7D,CAAC;YACF,MAAM,CAAC,gBAAgB,CAAC,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QAC1E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACnD,mBAAmB,CAAC,oBAA4B,CAAC,iBAAiB,CACjE,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAC5B,CAAC;YAEF,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CACjC,kBAAkB,CAChB,cAAc,EACd,KAAK,EAAE,aAAa;YACpB,IAAI,EAAG,aAAa;YACpB,IAAI,EAAG,YAAY;YACnB,wBAAwB,EACxB,sBAAsB,EACtB,OAAO,EAAE,YAAY;YACrB,gBAAgB,EAChB,YAAY,EACZ,cAAc,CACf,CACF,CAAC;YAEF,MAAM,SAAS,GAAG,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,EAAS,CAAC;YAErD,MAAM,GAAG,CAAC,KAAK,IAAI,EAAE;gBACnB,MAAM,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,YAAY,CAAC,CAAC,oBAAoB,CAAC,yCAAyC,CAAC,CAAC;YACrF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;QACzB,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;YAC3D,mBAAmB,CAAC,sBAA8B,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEjF,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CACjC,kBAAkB,CAChB,cAAc,EACd,IAAI,EAAE,aAAa;YACnB,KAAK,EAAE,aAAa;YACpB,aAAa,EAAE,YAAY;YAC3B,wBAAwB,EACxB,sBAAsB,EACtB,OAAO,EAAE,YAAY;YACrB,gBAAgB,EAChB,YAAY,EACZ,cAAc,CACf,CACF,CAAC;YAEF,MAAM,SAAS,GAAG,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,EAAS,CAAC;YAErD,MAAM,GAAG,CAAC,KAAK,IAAI,EAAE;gBACnB,MAAM,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,CAAC,oBAAoB,CACrE,CAAC,EAAE,WAAW;YACd,MAAM,CAAC,gBAAgB,CAAC;gBACtB,KAAK,EAAE,MAAM,CAAC,gBAAgB,CAAC;oBAC7B,IAAI,EAAE,YAAY;oBAClB,MAAM,EAAE,KAAK;oBACb,SAAS,EAAE,MAAM;oBACjB,WAAW,EAAE,GAAG;iBACjB,CAAC;aACH,CAAC,CACH,CAAC;YACF,MAAM,CAAC,cAAc,CAAC,CAAC,oBAAoB,CACzC,mDAAmD,CACpD,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,qBAAqB,GAAG,EAAE,GAAG,aAAa,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC;YAC5D,mBAAmB,CAAC,sBAA8B,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAEjF,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CACjC,kBAAkB,CAChB,cAAc,EACd,IAAI,EAAE,aAAa;YACnB,KAAK,EAAE,aAAa;YACpB,qBAAqB,EAAE,2BAA2B;YAClD,wBAAwB,EACxB,sBAAsB,EACtB,OAAO,EAAE,YAAY;YACrB,gBAAgB,EAChB,YAAY,EACZ,cAAc,CACf,CACF,CAAC;YAEF,MAAM,SAAS,GAAG,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,EAAS,CAAC;YAErD,MAAM,GAAG,CAAC,KAAK,IAAI,EAAE;gBACnB,MAAM,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,CAAC,oBAAoB,CACrE,EAAE,EAAE,sBAAsB;YAC1B,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CACnB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,+DAA+D,EAAE,KAAK,IAAI,EAAE;YAC5E,mBAAmB,CAAC,oBAA4B,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAEvE,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CACjC,kBAAkB,CAChB,cAAc,EACd,KAAK,EAAE,aAAa;YACpB,IAAI,EAAG,aAAa;YACpB,IAAI,EAAG,YAAY;YACnB,wBAAwB,EACxB,sBAAsB,EACtB,OAAO,EAAE,YAAY;YACrB,gBAAgB,EAChB,YAAY,EACZ,cAAc,CACf,CACF,CAAC;YAEF,MAAM,SAAS,GAAG,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,EAAS,CAAC;YAErD,MAAM,GAAG,CAAC,KAAK,IAAI,EAAE;gBACnB,MAAM,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAI,mBAAmB,CAAC,oBAA4B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAEpF,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,OAAO,CAC5B,MAAM,CAAC,gBAAgB,CAAC;gBACtB,SAAS,EAAE,MAAM,EAAE,0BAA0B;gBAC7C,QAAQ,EAAE,KAAK,EAAI,yBAAyB;gBAC5C,WAAW,EAAE,KAAK,EAAE,qBAAqB;gBACzC,UAAU,EAAE,KAAK,EAAG,qBAAqB;gBACzC,WAAW,EAAE,GAAG,EAAI,qBAAqB;gBACzC,UAAU,EAAE,GAAG,EAAK,qBAAqB;gBACzC,eAAe,EAAE,CAAC,EAAE,qBAAqB;aAC1C,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kEAAkE,EAAE,KAAK,IAAI,EAAE;YAC/E,mBAAmB,CAAC,oBAA4B,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAEvE,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CACjC,kBAAkB,CAChB,cAAc,EACd,KAAK,EAAE,aAAa;YACpB,IAAI,EAAG,aAAa;YACpB,IAAI,EAAG,YAAY;YACnB,wBAAwB,EACxB,sBAAsB,EACtB,OAAO,EAAE,YAAY;YACrB,gBAAgB,EAChB,YAAY,EACZ,cAAc,CACf,CACF,CAAC;YAEF,MAAM,SAAS,GAAG,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,EAAS,CAAC;YAErD,MAAM,GAAG,CAAC,KAAK,IAAI,EAAE;gBACnB,MAAM,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;YAEH,2DAA2D;YAC3D,MAAM,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,GAAG,MAAM,MAAM,CAAC,6BAA6B,CAAC,CAAC;YAClG,MAAM,CAAC,mBAAmB,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAC/C,MAAM,CAAC,oBAAoB,CAAC,CAAC,gBAAgB,EAAE,CAAC;QAClD,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4DAA4D,EAAE,KAAK,IAAI,EAAE;YACzE,mBAAmB,CAAC,oBAA4B,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAEvE,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CACjC,kBAAkB,CAChB,cAAc,EACd,KAAK,EAAE,aAAa;YACpB,IAAI,EAAG,aAAa;YACpB,IAAI,EAAG,YAAY;YACnB,wBAAwB,EACxB,sBAAsB,EACtB,OAAO,EAAE,YAAY;YACrB,gBAAgB,EAChB,YAAY,EACZ,cAAc,CACf,CACF,CAAC;YAEF,MAAM,SAAS,GAAG,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,EAAS,CAAC;YAErD,MAAM,GAAG,CAAC,KAAK,IAAI,EAAE;gBACnB,MAAM,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAI,mBAAmB,CAAC,oBAA4B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAEpF,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,OAAO,CAC/B,MAAM,CAAC,gBAAgB,CAAC;gBACtB,SAAS,EAAE,wBAAwB;gBACnC,YAAY,EAAE,OAAO;gBACrB,UAAU,EAAE,MAAM;aACnB,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC1D,IAAI,cAAoC,CAAC;YACzC,MAAM,WAAW,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;gBAC1C,cAAc,GAAG,OAAO,CAAC;YAC3B,CAAC,CAAC,CAAC;YACF,mBAAmB,CAAC,oBAA4B,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YAE/E,MAAM,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CACjC,kBAAkB,CAChB,cAAc,EACd,KAAK,EAAE,aAAa;YACpB,IAAI,EAAG,aAAa;YACpB,IAAI,EAAG,YAAY;YACnB,wBAAwB,EACxB,sBAAsB,EACtB,OAAO,EAAE,YAAY;YACrB,gBAAgB,EAChB,YAAY,EACZ,cAAc,CACf,CACF,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAEhD,MAAM,SAAS,GAAG,EAAE,cAAc,EAAE,EAAE,CAAC,EAAE,EAAE,EAAS,CAAC;YAErD,GAAG,CAAC,GAAG,EAAE;gBACP,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE/C,MAAM,GAAG,CAAC,KAAK,IAAI,EAAE;gBACnB,cAAe,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}