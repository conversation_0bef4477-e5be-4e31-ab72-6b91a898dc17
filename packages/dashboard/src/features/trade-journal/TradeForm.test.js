import { jsx as _jsx } from "react/jsx-runtime";
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { MemoryRouter, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';
import { f1Theme } from '@adhd-trading-dashboard/shared';
import TradeForm from './TradeForm';
// Mock the useTradeForm hook
vi.mock('./hooks/useTradeForm', () => ({
    useTradeForm: vi.fn(() => ({
        formValues: {
            date: '2023-01-01',
            symbol: 'AAPL',
            direction: 'long',
            quantity: '100',
            entryPrice: '150',
            exitPrice: '160',
            profit: '1000',
            result: 'win',
            notes: '',
        },
        handleChange: vi.fn(),
        handleSubmit: vi.fn((e) => {
            e.preventDefault();
            console.log('Form submitted');
        }),
        isSubmitting: false,
        isLoading: false,
        error: null,
        success: null,
        validationErrors: {},
        isNewTrade: true,
        activeTab: 'basic',
        handleTabChange: vi.fn(),
    })),
    MODEL_TYPE_OPTIONS: [],
    SESSION_OPTIONS: [],
    SETUP_OPTIONS: [],
    MARKET_OPTIONS: [],
    ENTRY_VERSION_OPTIONS: [],
    PATTERN_QUALITY_OPTIONS: [],
}));
// Mock the components used in tabs
vi.mock('./components/TimePicker', () => ({
    default: () => _jsx("div", { "data-testid": "mock-time-picker", children: "Time Picker" }),
}));
vi.mock('./components/SelectDropdown', () => ({
    default: () => _jsx("div", { "data-testid": "mock-select-dropdown", children: "Select Dropdown" }),
}));
vi.mock('./components/trade-analysis-section', () => ({
    TradeAnalysisSection: () => _jsx("div", { "data-testid": "mock-trade-analysis", children: "Trade Analysis" }),
}));
vi.mock('./components/trade-pattern-quality/PatternQualityAssessment', () => ({
    default: () => _jsx("div", { "data-testid": "mock-pattern-quality", children: "Pattern Quality" }),
}));
vi.mock('./components/trade-dol-analysis/TradeDOLAnalysis', () => ({
    default: () => _jsx("div", { "data-testid": "mock-dol-analysis", children: "DOL Analysis" }),
}));
describe('TradeForm Component', () => {
    beforeEach(() => {
        // Reset mocks before each test
        vi.clearAllMocks();
    });
    it('renders the form with tabs', () => {
        render(_jsx(MemoryRouter, { initialEntries: ['/trade/new'], children: _jsx(ThemeProvider, { theme: f1Theme, children: _jsx(Routes, { children: _jsx(Route, { path: "/trade/:id", element: _jsx(TradeForm, {}) }) }) }) }));
        // Check if the form and tabs are rendered
        expect(screen.getByText('Basic Info*')).toBeInTheDocument();
        expect(screen.getByText('Timing')).toBeInTheDocument();
        expect(screen.getByText('Risk Management')).toBeInTheDocument();
        expect(screen.getByText('Strategy')).toBeInTheDocument();
        expect(screen.getByText('Pattern Quality')).toBeInTheDocument();
        expect(screen.getByText('DOL Analysis')).toBeInTheDocument();
    });
    it('does not submit the form when clicking on tabs', async () => {
        const { useTradeForm } = await import('./hooks/useTradeForm');
        const mockHandleSubmit = vi.fn((e) => {
            e.preventDefault();
        });
        // Override the mock implementation for this test
        useTradeForm.mockImplementation(() => ({
            formValues: {
                date: '2023-01-01',
                symbol: 'AAPL',
                direction: 'long',
                quantity: '100',
                entryPrice: '150',
                exitPrice: '160',
                profit: '1000',
                result: 'win',
                notes: '',
            },
            handleChange: vi.fn(),
            handleSubmit: mockHandleSubmit,
            isSubmitting: false,
            isLoading: false,
            error: null,
            success: null,
            validationErrors: {},
            isNewTrade: true,
            activeTab: 'basic',
            handleTabChange: vi.fn(),
        }));
        render(_jsx(MemoryRouter, { initialEntries: ['/trade/new'], children: _jsx(ThemeProvider, { theme: f1Theme, children: _jsx(Routes, { children: _jsx(Route, { path: "/trade/:id", element: _jsx(TradeForm, {}) }) }) }) }));
        // Click on the Timing tab
        fireEvent.click(screen.getByText('Timing'));
        // Check that the form was not submitted
        expect(mockHandleSubmit).not.toHaveBeenCalled();
    });
    it('calls handleTabChange when clicking on a tab', async () => {
        const { useTradeForm } = await import('./hooks/useTradeForm');
        const mockHandleTabChange = vi.fn();
        // Override the mock implementation for this test
        useTradeForm.mockImplementation(() => ({
            formValues: {
                date: '2023-01-01',
                symbol: 'AAPL',
                direction: 'long',
                quantity: '100',
                entryPrice: '150',
                exitPrice: '160',
                profit: '1000',
                result: 'win',
                notes: '',
            },
            handleChange: vi.fn(),
            handleSubmit: vi.fn((e) => e.preventDefault()),
            isSubmitting: false,
            isLoading: false,
            error: null,
            success: null,
            validationErrors: {},
            isNewTrade: true,
            activeTab: 'basic',
            handleTabChange: mockHandleTabChange,
        }));
        render(_jsx(MemoryRouter, { initialEntries: ['/trade/new'], children: _jsx(ThemeProvider, { theme: f1Theme, children: _jsx(Routes, { children: _jsx(Route, { path: "/trade/:id", element: _jsx(TradeForm, {}) }) }) }) }));
        // Click on the Timing tab
        fireEvent.click(screen.getByText('Timing'));
        // Check that handleTabChange was called with the correct tab id
        expect(mockHandleTabChange).toHaveBeenCalledWith('timing');
    });
    it('submits the form only when clicking the submit button', async () => {
        const { useTradeForm } = await import('./hooks/useTradeForm');
        const mockHandleSubmit = vi.fn((e) => {
            e.preventDefault();
        });
        // Override the mock implementation for this test
        useTradeForm.mockImplementation(() => ({
            formValues: {
                date: '2023-01-01',
                symbol: 'AAPL',
                direction: 'long',
                quantity: '100',
                entryPrice: '150',
                exitPrice: '160',
                profit: '1000',
                result: 'win',
                notes: '',
            },
            handleChange: vi.fn(),
            handleSubmit: mockHandleSubmit,
            isSubmitting: false,
            isLoading: false,
            error: null,
            success: null,
            validationErrors: {},
            isNewTrade: true,
            activeTab: 'basic',
            handleTabChange: vi.fn(),
        }));
        render(_jsx(MemoryRouter, { initialEntries: ['/trade/new'], children: _jsx(ThemeProvider, { theme: f1Theme, children: _jsx(Routes, { children: _jsx(Route, { path: "/trade/:id", element: _jsx(TradeForm, {}) }) }) }) }));
        // Click on the submit button
        fireEvent.click(screen.getByText('Add Trade'));
        // Check that handleSubmit was called
        expect(mockHandleSubmit).toHaveBeenCalled();
    });
});
//# sourceMappingURL=TradeForm.test.js.map