/**
 * Trade Form Hook
 *
 * Custom hook for managing trade form state and logic
 */

import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';
import { useTradeFormData } from './useTradeFormData';
import { useTradeValidation } from './useTradeValidation';
import { useTradeCalculations } from './useTradeCalculations';
import { useTradeSubmission } from './useTradeSubmission';

// Options for dropdowns - Using actual trading models (not AI-generated generic ones)
export const MODEL_TYPE_OPTIONS = [
  { value: 'RD-Cont', label: 'RD-Cont' },
  { value: 'FVG-RD', label: 'FVG-RD' },
  { value: 'Combined', label: 'Combined' },
];

export const SESSION_OPTIONS = [
  { value: 'Pre-Market', label: 'Pre-Market' },
  { value: 'Regular Hours', label: 'Regular Hours' },
  { value: 'Power Hour', label: 'Power Hour' },
  { value: 'After Hours', label: 'After Hours' },
  { value: 'Overnight', label: 'Overnight' },
];

// SETUP_OPTIONS removed - using Setup Construction Matrix instead for modular setup building

export const MARKET_OPTIONS = [
  { value: 'Stocks', label: 'Stocks' },
  { value: 'Options', label: 'Options' },
  { value: 'Futures', label: 'Futures' },
  { value: 'Forex', label: 'Forex' },
  { value: 'Crypto', label: 'Crypto' },
  { value: 'Other', label: 'Other' },
];

export const ENTRY_VERSION_OPTIONS = [
  { value: 'First Entry', label: 'First Entry' },
  { value: 'Re-Entry', label: 'Re-Entry' },
  { value: 'Scale In', label: 'Scale In' },
  { value: 'Averaging Down', label: 'Averaging Down' },
  { value: 'Averaging Up', label: 'Averaging Up' },
];

export const PATTERN_QUALITY_OPTIONS = Array.from({ length: 10 }, (_, i) => ({
  value: String(i + 1),
  label: String(i + 1),
}));

/**
 * Main hook for managing trade form state and logic
 * @param tradeId The ID of the trade to load (optional)
 */
export const useTradeForm = (tradeId?: string) => {
  const navigate = useNavigate();

  // Enhanced debugging for tradeId parameter and hash-based routing
  console.log(`useTradeForm hook initialized with tradeId: "${tradeId}"`);

  // For hash-based routing, check the hash part of the URL
  const currentPath = window.location.hash.substring(1); // Remove the leading #
  console.log(`Current hash path in useTradeForm: ${currentPath}`);

  // Extract trade ID from URL if not provided directly
  let effectiveTradeId = tradeId;
  if (!effectiveTradeId && currentPath.includes('/trade/edit/')) {
    // Extract ID from URL path for edit mode
    const matches = currentPath.match(/\/trade\/edit\/([^\/]+)/);
    if (matches && matches[1]) {
      effectiveTradeId = matches[1];
      console.log(`Extracted trade ID from URL: ${effectiveTradeId}`);
    }
  }

  // Determine if we're creating a new trade or editing an existing one
  const isNewTrade = effectiveTradeId === 'new' || currentPath.includes('/trade/new');
  const isEditMode =
    (effectiveTradeId && effectiveTradeId !== 'new') ||
    (currentPath.includes('/trade/edit/') && !currentPath.includes('/trade/edit/new'));

  console.log(`useTradeForm - isNewTrade: ${isNewTrade}, isEditMode: ${isEditMode}`);

  // Use the form data hook to manage form state
  const {
    formValues,
    setFormValues,
    handleChange,
    isLoading,
    error,
    setError,
    success,
    setSuccess,
    tradeData,
  } = useTradeFormData(effectiveTradeId, isEditMode, isNewTrade);

  // Use the validation hook to validate form data
  const { validationErrors, validateBasicInfoTab } = useTradeValidation();

  // Use the calculations hook to calculate trade metrics
  const { calculateProfitLoss } = useTradeCalculations(formValues, setFormValues);

  // Wrapper function for validateBasicInfoTab to pass the current form values
  const validateBasicInfo = () => validateBasicInfoTab(formValues);

  // Use the submission hook to handle form submission
  const { handleSubmit, isSubmitting } = useTradeSubmission(
    formValues,
    isEditMode,
    isNewTrade,
    tradeData,
    validateBasicInfo,
    null, // No current tab validation needed
    null, // No active tab
    null, // No setActiveTab
    setError,
    setSuccess
  );

  return {
    formValues,
    setFormValues,
    handleChange,
    handleSubmit,
    isSubmitting,
    isLoading,
    error,
    success,
    validationErrors,
    isNewTrade,
    calculateProfitLoss,
  };
};
