/**
 * F1JournalHeader Component
 *
 * REFACTORED FROM: TradeJournal.tsx (72 lines → focused components)
 * F1 racing-themed header for the trade journal feature.
 *
 * BENEFITS:
 * - Focused responsibility (header only)
 * - F1 racing theme with journal-specific indicators
 * - Consistent with other F1Header components
 * - Better separation of concerns
 * - Reusable across journal contexts
 */

import React, { useState } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import { LegacyDataImport } from './index';

export interface F1JournalHeaderProps {
  /** Custom className */
  className?: string;
  /** Whether data is loading */
  isLoading?: boolean;
  /** Whether refresh is in progress */
  isRefreshing?: boolean;
  /** Number of trades */
  tradeCount?: number;
  /** Number of filtered trades */
  filteredCount?: number;
  /** Whether filters are active */
  hasActiveFilters?: boolean;
  /** Refresh handler */
  onRefresh?: () => void;
  /** Export handler */
  onExport?: () => void;
  /** Import handler */
  onImport?: () => void;
  /** Custom title */
  title?: string;
}

const HeaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
  margin-bottom: ${({ theme }) => theme.spacing?.xl || '32px'};
`;

const F1Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors?.surface || '#1f2937'} 0%,
    rgba(75, 85, 99, 0.1) 100%
  );
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  position: relative;
  overflow: hidden;

  /* F1 Racing accent line */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(
      90deg,
      ${({ theme }) => theme.colors?.primary || '#dc2626'} 0%,
      transparent 100%
    );
  }
`;

const F1Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes?.h2 || '1.5rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

  span {
    color: ${({ theme }) => theme.colors?.primary || '#dc2626'};
    font-weight: 800;
  }
`;

const JournalIndicator = styled.div<{ $hasData: boolean }>`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  color: ${({ $hasData, theme }) =>
    $hasData ? theme.colors?.success || '#22c55e' : theme.colors?.textSecondary || '#9ca3af'};
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};
  border-radius: ${({ theme }) => theme.borderRadius?.full || '9999px'};
  border: 1px solid
    ${({ $hasData, theme }) =>
      $hasData ? theme.colors?.success || '#22c55e' : theme.colors?.border || '#4b5563'};
  background: ${({ $hasData, theme }) =>
    $hasData ? `${theme.colors?.success || '#22c55e'}20` : 'transparent'};

  &::before {
    content: '📊';
    font-size: 12px;
  }
`;

const SubHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
`;

const TitleSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const SubTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes?.xxl || '1.875rem'};
  margin: 0;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-weight: 600;
`;

const StatsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const StatBadge = styled.span<{ $variant: 'total' | 'filtered' | 'active' }>`
  background: ${({ $variant, theme }) => {
    switch ($variant) {
      case 'total':
        return theme.colors?.surface || '#1f2937';
      case 'filtered':
        return theme.colors?.primary || '#dc2626';
      case 'active':
        return theme.colors?.success || '#22c55e';
      default:
        return theme.colors?.surface || '#1f2937';
    }
  }}20;
  color: ${({ $variant, theme }) => {
    switch ($variant) {
      case 'total':
        return theme.colors?.textPrimary || '#ffffff';
      case 'filtered':
        return theme.colors?.primary || '#dc2626';
      case 'active':
        return theme.colors?.success || '#22c55e';
      default:
        return theme.colors?.textPrimary || '#ffffff';
    }
  }};
  padding: ${({ theme }) => theme.spacing?.xxs || '2px'}
    ${({ theme }) => theme.spacing?.sm || '8px'};
  border-radius: ${({ theme }) => theme.borderRadius?.full || '9999px'};
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  font-weight: 600;
  border: 1px solid
    ${({ $variant, theme }) => {
      switch ($variant) {
        case 'total':
          return theme.colors?.border || '#4b5563';
        case 'filtered':
          return theme.colors?.primary || '#dc2626';
        case 'active':
          return theme.colors?.success || '#22c55e';
        default:
          return theme.colors?.border || '#4b5563';
      }
    }}40;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const ActionsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;

const ActionButton = styled.button<{ $variant: 'primary' | 'secondary' }>`
  background: ${({ $variant, theme }) =>
    $variant === 'primary' ? theme.colors?.primary || '#dc2626' : 'transparent'};
  color: ${({ $variant, theme }) =>
    $variant === 'primary'
      ? theme.colors?.textInverse || '#ffffff'
      : theme.colors?.textSecondary || '#9ca3af'};
  border: 1px solid
    ${({ $variant, theme }) =>
      $variant === 'primary'
        ? theme.colors?.primary || '#dc2626'
        : theme.colors?.border || '#4b5563'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  padding: ${({ theme }) => theme.spacing?.sm || '8px'}
    ${({ theme }) => theme.spacing?.md || '12px'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  min-width: 100px;
  justify-content: center;

  &:hover:not(:disabled) {
    background: ${({ $variant, theme }) =>
      $variant === 'primary'
        ? theme.colors?.primaryDark || '#b91c1c'
        : theme.colors?.surface || '#1f2937'};
    transform: translateY(-1px);
    box-shadow: 0 4px 8px
      ${({ $variant, theme }) =>
        $variant === 'primary' ? `${theme.colors?.primary || '#dc2626'}40` : 'rgba(0, 0, 0, 0.1)'};
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const AddTradeLink = styled(Link)`
  background: ${({ theme }) => theme.colors?.success || '#22c55e'};
  color: ${({ theme }) => theme.colors?.textInverse || '#ffffff'};
  border: 1px solid ${({ theme }) => theme.colors?.success || '#22c55e'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  padding: ${({ theme }) => theme.spacing?.sm || '8px'}
    ${({ theme }) => theme.spacing?.md || '12px'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 600;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  min-width: 120px;
  justify-content: center;

  &:hover {
    background: ${({ theme }) => theme.colors?.successDark || '#16a34a'};
    transform: translateY(-1px);
    box-shadow: 0 4px 8px ${({ theme }) => theme.colors?.success || '#22c55e'}40;
  }

  &:active {
    transform: translateY(0);
  }
`;

// Modal styles for import functionality
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
`;

const ModalContent = styled.div`
  background: ${({ theme }) => theme.colors?.surface || '#1f2937'};
  border: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || '#4b5563'};
`;

const ModalTitle = styled.h2`
  margin: 0;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-size: ${({ theme }) => theme.fontSizes?.xl || '1.25rem'};
  font-weight: 600;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  font-size: 24px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;

  &:hover {
    background: ${({ theme }) => theme.colors?.surface || '#1f2937'};
    color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  }
`;

/**
 * F1JournalHeader Component
 *
 * PATTERN: F1 Header Pattern
 * - Racing-inspired styling with journal-specific indicators
 * - Trade count and filter status indicators
 * - Consistent with F1 design system
 * - Accessible and responsive
 * - Professional trading journal appearance
 */
export const F1JournalHeader: React.FC<F1JournalHeaderProps> = ({
  className,
  isLoading = false,
  isRefreshing = false,
  tradeCount = 0,
  filteredCount,
  hasActiveFilters = false,
  onRefresh,
  onExport,
  onImport,
  title = 'Trade Journal',
}) => {
  const [showImportModal, setShowImportModal] = useState(false);
  const hasData = tradeCount > 0;
  const showFilteredCount =
    hasActiveFilters && filteredCount !== undefined && filteredCount !== tradeCount;

  const handleImportClick = () => {
    setShowImportModal(true);
    onImport?.();
  };

  const handleImportComplete = () => {
    setShowImportModal(false);
    // Trigger refresh if available
    onRefresh?.();
  };

  return (
    <HeaderContainer className={className}>
      {/* F1 Racing Header */}
      <F1Header>
        <F1Title>
          🏎️ TRADING <span>JOURNAL</span>
        </F1Title>
        <JournalIndicator $hasData={hasData}>
          {hasData ? `${tradeCount} TRADES` : 'NO TRADES'}
        </JournalIndicator>
      </F1Header>

      {/* Sub Header */}
      <SubHeader>
        <TitleSection>
          <SubTitle>{title}</SubTitle>
          <StatsContainer>
            {hasData && (
              <StatBadge $variant="total">📊 {tradeCount.toLocaleString()} total</StatBadge>
            )}
            {showFilteredCount && (
              <StatBadge $variant="filtered">
                🔍 {filteredCount!.toLocaleString()} filtered
              </StatBadge>
            )}
            {hasActiveFilters && <StatBadge $variant="active">⚡ filters active</StatBadge>}
          </StatsContainer>
        </TitleSection>

        <ActionsContainer>
          {onRefresh && (
            <ActionButton
              $variant="secondary"
              onClick={onRefresh}
              disabled={isLoading}
              title={isLoading ? 'Refreshing trades...' : 'Refresh trade data'}
            >
              {isLoading || isRefreshing ? '⏳' : '🔄'}
              {isLoading ? 'Refreshing' : 'Refresh'}
            </ActionButton>
          )}

          <ActionButton
            $variant="secondary"
            onClick={handleImportClick}
            disabled={isLoading}
            title="Import legacy trade data from CSV"
          >
            📥 Import
          </ActionButton>

          {onExport && (
            <ActionButton
              $variant="secondary"
              onClick={onExport}
              disabled={isLoading || !hasData}
              title="Export trade data"
            >
              📊 Export
            </ActionButton>
          )}

          <AddTradeLink to="/trade/new" title="Add new trade">
            ➕ Add Trade
          </AddTradeLink>
        </ActionsContainer>
      </SubHeader>

      {/* Import Modal */}
      {showImportModal && (
        <ModalOverlay onClick={() => setShowImportModal(false)}>
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <ModalHeader>
              <ModalTitle>🏎️ Import Legacy Trade Data</ModalTitle>
              <CloseButton onClick={() => setShowImportModal(false)}>×</CloseButton>
            </ModalHeader>
            <LegacyDataImport onImportComplete={handleImportComplete} />
          </ModalContent>
        </ModalOverlay>
      )}
    </HeaderContainer>
  );
};

export default F1JournalHeader;
