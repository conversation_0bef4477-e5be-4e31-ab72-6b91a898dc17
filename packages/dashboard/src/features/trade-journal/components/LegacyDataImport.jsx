import React, { useState, useCallback } from 'react';
import { tradeStorageService } from '@adhd-trading-dashboard/shared/services';

// Simple icon components to replace lucide-react
const Upload = () => <span>📤</span>;
const FileText = () => <span>📄</span>;
const CheckCircle = () => <span>✅</span>;
const XCircle = () => <span>❌</span>;
const AlertCircle = () => <span>⚠️</span>;
const Download = () => <span>💾</span>;

const CSVImportTool = ({ onImportComplete }) => {
  const [file, setFile] = useState(null);
  const [csvData, setCsvData] = useState(null);
  const [mappedData, setMappedData] = useState(null);
  const [importStatus, setImportStatus] = useState('idle'); // idle, processing, preview, imported
  const [stats, setStats] = useState(null);

  // Smart column mapping - maps CSV columns to your actual database schema (TradeRecord)
  const COLUMN_MAPPINGS = {
    // Core TradeRecord fields
    date: ['date', 'trade date', 'trading date'],
    model_type: ['model type', 'trading model', 'model', 'strategy type'],
    direction: ['direction', 'side', 'position', 'long/short'],
    market: ['market', 'symbol', 'instrument', 'ticker'],

    // Price and performance fields
    entry_price: ['entry price', 'entry', 'open price', 'buy price'],
    exit_price: ['exit price', 'exit', 'close price', 'sell price'],
    achieved_pl: ['achieved p/l', 'pnl', 'profit loss', 'p&l', 'profit/loss', 'pl'],
    r_multiple: ['r-multiple', 'r multiple', 'r', 'risk reward', 'rr'],
    risk_points: ['risk (points)', 'risk points', 'risk', 'stop distance'],
    no_of_contracts: ['no. of contracts', 'contracts', 'quantity', 'size', 'position size'],

    // Trade quality and outcome
    win_loss: ['win/loss', 'result', 'outcome', 'win loss'],
    pattern_quality_rating: [
      'pattern quality rating (1-5)',
      'pattern quality',
      'quality',
      'setup quality',
    ],

    // Session and timing
    session: ['session (time block)', 'session', 'time block', 'trading session'],
    entry_time: ['entry time', 'open time', 'start time'],
    exit_time: ['exit time', 'close time', 'end time'],

    // Setup information
    setup: ['setup', 'trade setup', 'pattern'],
    primary_setup: ['primary setup', 'main setup', 'setup type'],
    secondary_setup: ['secondary setup', 'additional setup'],

    // Additional fields
    notes: ['notes', 'comments', 'remarks', 'description'],
    rd_type: ['rd type', 'reversal type', 'rd'],
    dol_target: ['dol target', 'target', 'profit target'],
    draw_on_liquidity: ['draw on liquidity', 'dol', 'liquidity'],
  };

  // Valid trading models from your schema
  const VALID_TRADING_MODELS = ['RD-Cont', 'FVG-RD', 'Combined'];

  // Valid sessions from your application
  const VALID_SESSIONS = [
    'NY Open',
    'Lunch Macro',
    'MOC',
    'London Open',
    'Asian Session',
    'Pre-Market',
    'After Hours',
    'NY AM',
    'NY PM',
  ];

  // Valid markets
  const VALID_MARKETS = ['MNQ', 'NQ', 'ES', 'MES', 'YM', 'MYM', 'RTY', 'M2K'];

  const handleFileUpload = useCallback((event) => {
    const uploadedFile = event.target.files[0];
    if (uploadedFile && uploadedFile.type === 'text/csv') {
      setFile(uploadedFile);
      setImportStatus('processing');
      parseCSV(uploadedFile);
    }
  }, []);

  const parseCSV = async (file) => {
    const text = await file.text();
    const lines = text.split('\n');
    const headers = lines[0].split(',').map((h) => h.trim().toLowerCase());

    const rows = lines
      .slice(1)
      .filter((line) => line.trim()) // Remove empty lines
      .map((line) => {
        const values = line.split(',');
        const row = {};
        headers.forEach((header, index) => {
          row[header] = values[index]?.trim() || '';
        });
        return row;
      });

    setCsvData({ headers, rows });
    mapColumns(headers, rows);
  };

  const mapColumns = (headers, rows) => {
    const mapped = rows
      .map((row) => {
        const tradeRecord = {};

        // Smart mapping logic - map to TradeRecord schema
        Object.entries(COLUMN_MAPPINGS).forEach(([dbField, possibleHeaders]) => {
          const matchedHeader = headers.find((h) =>
            possibleHeaders.some((ph) => h.toLowerCase().includes(ph.toLowerCase()))
          );

          if (matchedHeader && row[matchedHeader]) {
            let value = row[matchedHeader].trim();

            // Field-specific transformations to match TradeRecord schema
            if (dbField === 'model_type') {
              // Map to valid trading models
              const model = VALID_TRADING_MODELS.find(
                (vm) =>
                  value.toLowerCase().includes(vm.toLowerCase()) ||
                  value
                    .toLowerCase()
                    .replace(/[-\s]/g, '')
                    .includes(vm.toLowerCase().replace(/[-\s]/g, ''))
              );
              tradeRecord[dbField] = model || 'Combined'; // Default to Combined if no match
            } else if (dbField === 'direction') {
              // Standardize direction to 'Long' | 'Short'
              const lower = value.toLowerCase();
              if (lower.includes('long') || lower.includes('buy') || lower === 'l') {
                tradeRecord[dbField] = 'Long';
              } else if (lower.includes('short') || lower.includes('sell') || lower === 's') {
                tradeRecord[dbField] = 'Short';
              } else {
                tradeRecord[dbField] = 'Long'; // Default to Long
              }
            } else if (dbField === 'win_loss') {
              // Standardize win/loss to 'Win' | 'Loss'
              const lower = value.toLowerCase();
              if (lower.includes('win') || lower.includes('profit') || lower === 'w') {
                tradeRecord[dbField] = 'Win';
              } else if (lower.includes('loss') || lower.includes('lose') || lower === 'l') {
                tradeRecord[dbField] = 'Loss';
              }
            } else if (dbField === 'session') {
              // Map to valid sessions
              const session = VALID_SESSIONS.find(
                (vs) =>
                  value.toLowerCase().includes(vs.toLowerCase()) ||
                  vs.toLowerCase().includes(value.toLowerCase())
              );
              tradeRecord[dbField] = session || value; // Keep original if no match
            } else if (dbField === 'market') {
              // Map to valid markets
              const market = VALID_MARKETS.find((vm) =>
                value.toLowerCase().includes(vm.toLowerCase())
              );
              tradeRecord[dbField] = market || 'MNQ'; // Default to MNQ
            } else if (
              ['entry_price', 'exit_price', 'achieved_pl', 'r_multiple', 'risk_points'].includes(
                dbField
              )
            ) {
              // Clean up numeric fields
              const numericValue = parseFloat(value.replace(/[^-0-9.]/g, ''));
              tradeRecord[dbField] = isNaN(numericValue) ? null : numericValue;
            } else if (dbField === 'pattern_quality_rating') {
              // Ensure quality rating is between 1-5
              const rating = parseInt(value) || 3;
              tradeRecord[dbField] = Math.max(1, Math.min(5, rating));
            } else if (dbField === 'no_of_contracts') {
              // Ensure contracts is a positive number
              const contracts = parseFloat(value) || 1;
              tradeRecord[dbField] = Math.max(0.1, contracts);
            } else if (['entry_time', 'exit_time'].includes(dbField)) {
              // Handle time fields - ensure proper format
              tradeRecord[dbField] = value.includes(':') ? value : null;
            } else {
              // String fields - just clean and assign
              tradeRecord[dbField] = value;
            }
          }
        });

        // Set required defaults for TradeRecord
        if (!tradeRecord.date) return null;
        if (!tradeRecord.model_type) tradeRecord.model_type = 'Combined';
        if (!tradeRecord.direction) tradeRecord.direction = 'Long';
        if (!tradeRecord.market) tradeRecord.market = 'MNQ';
        if (!tradeRecord.pattern_quality_rating) tradeRecord.pattern_quality_rating = 3;
        if (!tradeRecord.no_of_contracts) tradeRecord.no_of_contracts = 1;

        // Only include trades with essential data
        return tradeRecord.date && (tradeRecord.entry_price || tradeRecord.exit_price)
          ? tradeRecord
          : null;
      })
      .filter(Boolean);

    setMappedData(mapped);

    // Generate comprehensive stats
    const validTrades = mapped.filter(
      (t) => t.date && t.model_type && (t.entry_price || t.exit_price)
    );
    const unmappedModels = mapped.filter(
      (t) => t.model_type && !VALID_TRADING_MODELS.includes(t.model_type)
    ).length;
    const missingPrices = mapped.filter((t) => t.date && !t.entry_price && !t.exit_price).length;
    const winningTrades = mapped.filter((t) => t.win_loss === 'Win').length;
    const losingTrades = mapped.filter((t) => t.win_loss === 'Loss').length;

    setStats({
      totalRows: rows.length,
      validTrades: validTrades.length,
      unmappedModels,
      missingPrices,
      winningTrades,
      losingTrades,
      skipped: rows.length - validTrades.length,
      winRate:
        validTrades.length > 0
          ? ((winningTrades / (winningTrades + losingTrades)) * 100).toFixed(1)
          : 0,
    });

    setImportStatus('preview');
  };

  const handleImport = async () => {
    setImportStatus('processing');

    try {
      // Convert mapped data to CompleteTradeData and save to IndexedDB
      const importPromises = mappedData.map(async (tradeRecord) => {
        // The tradeRecord is already in the correct TradeRecord format from mapping
        const cleanedTradeRecord = {
          ...tradeRecord,
          // Ensure timestamps
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        // Create setup data if we have setup information
        const setupData =
          tradeRecord.primary_setup || tradeRecord.secondary_setup
            ? {
                primary_setup: tradeRecord.primary_setup || null,
                secondary_setup: tradeRecord.secondary_setup || null,
                liquidity_taken: null,
                additional_fvgs: null,
                dol: tradeRecord.dol_target || null,
              }
            : null;

        // Create FVG details if we have FVG-related information
        const fvgDetails =
          tradeRecord.rd_type || tradeRecord.draw_on_liquidity
            ? {
                fvg_date: tradeRecord.date,
                rd_type: tradeRecord.rd_type || null,
                entry_version: null,
                draw_on_liquidity: tradeRecord.draw_on_liquidity || null,
              }
            : null;

        // Create analysis data with import notes
        const analysisData = {
          tradingview_link: null,
          dol_target_type: tradeRecord.dol_target || null,
          beyond_target: null,
          clustering: null,
          path_quality: null,
          idr_context: null,
          sequential_fvg_rd: null,
          dol_notes: `Imported from CSV on ${new Date().toLocaleDateString()}. Original notes: ${
            tradeRecord.notes || 'None'
          }`,
        };

        // Create complete trade data structure matching your schema
        const completeTradeData = {
          trade: cleanedTradeRecord,
          fvg_details: fvgDetails,
          setup: setupData,
          analysis: analysisData,
        };

        return tradeStorageService.saveTradeWithDetails(completeTradeData);
      });

      await Promise.all(importPromises);
      setImportStatus('imported');

      // Call completion callback after successful import
      setTimeout(() => {
        onImportComplete?.();
      }, 2000); // Give user time to see success message
    } catch (error) {
      console.error('Import failed:', error);
      setImportStatus('preview'); // Return to preview on error
      // You could add error state handling here
    }
  };

  const downloadCleanedData = () => {
    const csv = [
      // Headers
      Object.keys(mappedData[0] || {}).join(','),
      // Data rows
      ...mappedData.map((trade) =>
        Object.values(trade)
          .map((v) => `"${v}"`)
          .join(',')
      ),
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'cleaned_trades.csv';
    a.click();
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Trade Data Migration Tool</h2>
        <p className="text-gray-600">
          Import your Google Sheets CSV exports and clean them for your new trading app
        </p>
      </div>

      {/* Upload Section */}
      {importStatus === 'idle' && (
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          <div className="mx-auto text-4xl mb-4">
            <Upload />
          </div>
          <div className="mb-4">
            <label htmlFor="csv-upload" className="cursor-pointer">
              <span className="text-lg font-medium text-blue-600 hover:text-blue-500">
                Upload CSV file
              </span>
              <input
                id="csv-upload"
                type="file"
                accept=".csv"
                onChange={handleFileUpload}
                className="sr-only"
              />
            </label>
          </div>
          <p className="text-sm text-gray-500">Select your Google Sheets CSV export</p>
        </div>
      )}

      {/* Processing */}
      {importStatus === 'processing' && (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">Processing your CSV data...</p>
        </div>
      )}

      {/* Preview */}
      {importStatus === 'preview' && stats && (
        <div className="space-y-6">
          {/* Stats */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3">Import Summary</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
              <div className="flex items-center">
                <FileText />
                <span className="ml-2">{stats.totalRows} total rows</span>
              </div>
              <div className="flex items-center">
                <CheckCircle />
                <span className="ml-2">{stats.validTrades} valid trades</span>
              </div>
              <div className="flex items-center">
                <AlertCircle />
                <span className="ml-2">{stats.unmappedModels} unknown models</span>
              </div>
              <div className="flex items-center">
                <XCircle />
                <span className="ml-2">{stats.skipped} skipped</span>
              </div>
            </div>

            {/* Additional Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm pt-2 border-t border-gray-200">
              <div className="flex items-center">
                <span className="text-green-600">✅</span>
                <span className="ml-2">{stats.winningTrades} wins</span>
              </div>
              <div className="flex items-center">
                <span className="text-red-600">❌</span>
                <span className="ml-2">{stats.losingTrades} losses</span>
              </div>
              <div className="flex items-center">
                <span className="text-blue-600">📊</span>
                <span className="ml-2">{stats.winRate}% win rate</span>
              </div>
              <div className="flex items-center">
                <span className="text-yellow-600">⚠️</span>
                <span className="ml-2">{stats.missingPrices} missing prices</span>
              </div>
            </div>
          </div>

          {/* Preview Table */}
          <div className="bg-white border rounded-lg overflow-hidden">
            <div className="px-4 py-3 bg-gray-50 border-b">
              <h3 className="text-lg font-semibold">Preview (First 5 trades)</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    {Object.keys(mappedData[0] || {}).map((key) => (
                      <th
                        key={key}
                        className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase"
                      >
                        {key}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {mappedData.slice(0, 5).map((trade, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      {Object.entries(trade).map(([field, value], i) => (
                        <td key={i} className="px-3 py-2 text-sm text-gray-900">
                          <span
                            className={
                              // Highlight validation issues
                              (field === 'model_type' && !VALID_TRADING_MODELS.includes(value)) ||
                              (field === 'market' && !VALID_MARKETS.includes(value)) ||
                              (field === 'session' && value && !VALID_SESSIONS.includes(value)) ||
                              (field === 'direction' && !['Long', 'Short'].includes(value)) ||
                              (field === 'win_loss' && value && !['Win', 'Loss'].includes(value))
                                ? 'bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs'
                                : field === 'pattern_quality_rating' && (value < 1 || value > 5)
                                ? 'bg-red-100 text-red-800 px-2 py-1 rounded text-xs'
                                : ''
                            }
                            title={
                              // Add tooltips for validation issues
                              field === 'model_type' && !VALID_TRADING_MODELS.includes(value)
                                ? `Unknown model: ${value}. Will default to 'Combined'.`
                                : field === 'market' && !VALID_MARKETS.includes(value)
                                ? `Unknown market: ${value}. Will default to 'MNQ'.`
                                : field === 'pattern_quality_rating' && (value < 1 || value > 5)
                                ? `Invalid quality rating: ${value}. Must be 1-5.`
                                : ''
                            }
                          >
                            {typeof value === 'number' && value !== null
                              ? value.toFixed(2)
                              : value || '—'}
                          </span>
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-4">
            <button
              onClick={handleImport}
              className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-medium"
            >
              Import {stats.validTrades} Trades
            </button>
            <button
              onClick={downloadCleanedData}
              className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 font-medium flex items-center gap-2"
            >
              <Download />
              Download Cleaned CSV
            </button>
            <button
              onClick={() => setImportStatus('idle')}
              className="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 font-medium"
            >
              Start Over
            </button>
          </div>
        </div>
      )}

      {/* Success */}
      {importStatus === 'imported' && (
        <div className="text-center py-8">
          <div className="mx-auto text-6xl mb-4">
            <CheckCircle />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Import Complete!</h3>
          <p className="text-gray-600 mb-6">
            {stats?.validTrades} trades have been imported into your app
          </p>
          <button
            onClick={() => setImportStatus('idle')}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
          >
            Import Another File
          </button>
        </div>
      )}
    </div>
  );
};

export default CSVImportTool;
