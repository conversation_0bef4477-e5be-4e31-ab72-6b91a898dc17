import React, { useState, useCallback } from 'react';
import { tradeStorageService } from '@adhd-trading-dashboard/shared/services';

// Simple icon components to replace lucide-react
const Upload = () => <span>📤</span>;
const FileText = () => <span>📄</span>;
const CheckCircle = () => <span>✅</span>;
const XCircle = () => <span>❌</span>;
const AlertCircle = () => <span>⚠️</span>;
const Download = () => <span>💾</span>;

const CSVImportTool = ({ onImportComplete }) => {
  const [file, setFile] = useState(null);
  const [csvData, setCsvData] = useState(null);
  const [mappedData, setMappedData] = useState(null);
  const [importStatus, setImportStatus] = useState('idle'); // idle, processing, preview, imported
  const [stats, setStats] = useState(null);

  // Smart column mapping - maps CSV columns to your trade structure
  const COLUMN_MAPPINGS = {
    // Core essentials
    date: ['date'],
    tradingModel: ['model type'],
    direction: ['direction'],
    market: ['market'],
    entryPrice: ['entry price'],
    exitPrice: ['exit price'],
    rMultiple: ['r-multiple'],
    result: ['win/loss'],
    patternQuality: ['pattern quality rating (1-5)'],
    pnl: ['achieved p/l'],

    // Important context
    session: ['session (time block)'],
    contracts: ['no. of contracts'],
    risk: ['risk (points)'],
    notes: ['notes'],
    primarySetup: ['primary setup'],
    secondarySetup: ['secondary setup'],
  };

  // Your actual trading models
  const VALID_TRADING_MODELS = ['RD-Cont', 'FVG-RD', 'Combined'];

  const handleFileUpload = useCallback((event) => {
    const uploadedFile = event.target.files[0];
    if (uploadedFile && uploadedFile.type === 'text/csv') {
      setFile(uploadedFile);
      setImportStatus('processing');
      parseCSV(uploadedFile);
    }
  }, []);

  const parseCSV = async (file) => {
    const text = await file.text();
    const lines = text.split('\n');
    const headers = lines[0].split(',').map((h) => h.trim().toLowerCase());

    const rows = lines
      .slice(1)
      .filter((line) => line.trim()) // Remove empty lines
      .map((line) => {
        const values = line.split(',');
        const row = {};
        headers.forEach((header, index) => {
          row[header] = values[index]?.trim() || '';
        });
        return row;
      });

    setCsvData({ headers, rows });
    mapColumns(headers, rows);
  };

  const mapColumns = (headers, rows) => {
    const mapped = rows
      .map((row) => {
        const trade = {};

        // Smart mapping logic
        Object.entries(COLUMN_MAPPINGS).forEach(([tradeField, possibleHeaders]) => {
          const matchedHeader = headers.find((h) => possibleHeaders.some((ph) => h.includes(ph)));

          if (matchedHeader && row[matchedHeader]) {
            let value = row[matchedHeader];

            // Clean up specific fields
            if (tradeField === 'tradingModel') {
              // Map to your actual models
              const model = VALID_TRADING_MODELS.find(
                (vm) =>
                  value.toLowerCase().includes(vm.toLowerCase()) ||
                  value
                    .toLowerCase()
                    .replace(/[-\s]/g, '')
                    .includes(vm.toLowerCase().replace(/[-\s]/g, ''))
              );
              trade[tradeField] = model || value; // Keep original if no match
            } else if (tradeField === 'result') {
              // Standardize win/loss
              const lower = value.toLowerCase();
              trade[tradeField] = lower.includes('win')
                ? 'WIN'
                : lower.includes('loss')
                ? 'LOSS'
                : value;
            } else if (
              ['pnl', 'entryPrice', 'exitPrice', 'rMultiple', 'risk'].includes(tradeField)
            ) {
              // Clean up numeric fields
              trade[tradeField] = parseFloat(value.replace(/[^-0-9.]/g, '')) || 0;
            } else if (tradeField === 'patternQuality') {
              // Ensure quality rating is a number
              trade[tradeField] = parseInt(value) || 0;
            } else if (tradeField === 'contracts') {
              // Ensure contracts is a number
              trade[tradeField] = parseInt(value) || 1;
            } else {
              trade[tradeField] = value;
            }
          }
        });

        // Only include trades with essential data (date, model, and either entry/exit price)
        return trade.date && trade.tradingModel && (trade.entryPrice || trade.exitPrice)
          ? trade
          : null;
      })
      .filter(Boolean);

    setMappedData(mapped);

    // Generate stats
    const validTrades = mapped.filter(
      (t) => t.date && t.tradingModel && (t.entryPrice || t.exitPrice)
    );
    const unmappedModels = mapped.filter(
      (t) => t.tradingModel && !VALID_TRADING_MODELS.includes(t.tradingModel)
    ).length;

    setStats({
      totalRows: rows.length,
      validTrades: validTrades.length,
      unmappedModels,
      skipped: rows.length - validTrades.length,
    });

    setImportStatus('preview');
  };

  const handleImport = async () => {
    setImportStatus('processing');

    try {
      // Convert mapped data to trade records and save to IndexedDB
      const importPromises = mappedData.map(async (trade) => {
        // Convert CSV data to trade record format
        const tradeRecord = {
          date: trade.date || new Date().toISOString().split('T')[0],
          model_type: trade.tradingModel || 'Combined',
          direction: trade.direction === 'long' ? 'Long' : 'Short',
          entry_price: trade.entryPrice || 0,
          exit_price: trade.exitPrice || 0,
          achieved_pl: trade.pnl || 0,
          r_multiple: trade.rMultiple || 0,
          pattern_quality_rating: trade.patternQuality || 5,
          market: trade.market || 'MNQ',
          win_loss: trade.result === 'WIN' ? 'Win' : 'Loss',
          notes: `Imported from CSV - Original setup: ${trade.primarySetup || 'N/A'}`,
        };

        // Create complete trade data structure
        const completeTradeData = {
          trade: tradeRecord,
          fvg_details: null,
          setup: null,
          analysis: null,
        };

        return tradeStorageService.saveTradeWithDetails(completeTradeData);
      });

      await Promise.all(importPromises);
      setImportStatus('imported');

      // Call completion callback after successful import
      setTimeout(() => {
        onImportComplete?.();
      }, 2000); // Give user time to see success message
    } catch (error) {
      console.error('Import failed:', error);
      setImportStatus('preview'); // Return to preview on error
      // You could add error state handling here
    }
  };

  const downloadCleanedData = () => {
    const csv = [
      // Headers
      Object.keys(mappedData[0] || {}).join(','),
      // Data rows
      ...mappedData.map((trade) =>
        Object.values(trade)
          .map((v) => `"${v}"`)
          .join(',')
      ),
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'cleaned_trades.csv';
    a.click();
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Trade Data Migration Tool</h2>
        <p className="text-gray-600">
          Import your Google Sheets CSV exports and clean them for your new trading app
        </p>
      </div>

      {/* Upload Section */}
      {importStatus === 'idle' && (
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          <div className="mx-auto text-4xl mb-4">
            <Upload />
          </div>
          <div className="mb-4">
            <label htmlFor="csv-upload" className="cursor-pointer">
              <span className="text-lg font-medium text-blue-600 hover:text-blue-500">
                Upload CSV file
              </span>
              <input
                id="csv-upload"
                type="file"
                accept=".csv"
                onChange={handleFileUpload}
                className="sr-only"
              />
            </label>
          </div>
          <p className="text-sm text-gray-500">Select your Google Sheets CSV export</p>
        </div>
      )}

      {/* Processing */}
      {importStatus === 'processing' && (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">Processing your CSV data...</p>
        </div>
      )}

      {/* Preview */}
      {importStatus === 'preview' && stats && (
        <div className="space-y-6">
          {/* Stats */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3">Import Summary</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="flex items-center">
                <FileText />
                <span className="ml-2">{stats.totalRows} total rows</span>
              </div>
              <div className="flex items-center">
                <CheckCircle />
                <span className="ml-2">{stats.validTrades} valid trades</span>
              </div>
              <div className="flex items-center">
                <AlertCircle />
                <span className="ml-2">{stats.unmappedModels} unknown models</span>
              </div>
              <div className="flex items-center">
                <XCircle />
                <span className="ml-2">{stats.skipped} skipped</span>
              </div>
            </div>
          </div>

          {/* Preview Table */}
          <div className="bg-white border rounded-lg overflow-hidden">
            <div className="px-4 py-3 bg-gray-50 border-b">
              <h3 className="text-lg font-semibold">Preview (First 5 trades)</h3>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    {Object.keys(mappedData[0] || {}).map((key) => (
                      <th
                        key={key}
                        className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase"
                      >
                        {key}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {mappedData.slice(0, 5).map((trade, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      {Object.values(trade).map((value, i) => (
                        <td key={i} className="px-3 py-2 text-sm text-gray-900">
                          <span
                            className={
                              Object.keys(mappedData[0] || {})[i] === 'tradingModel' &&
                              !VALID_TRADING_MODELS.includes(value)
                                ? 'bg-yellow-100 text-yellow-800 px-2 py-1 rounded'
                                : ''
                            }
                          >
                            {typeof value === 'number' ? value.toFixed(2) : value}
                          </span>
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-4">
            <button
              onClick={handleImport}
              className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 font-medium"
            >
              Import {stats.validTrades} Trades
            </button>
            <button
              onClick={downloadCleanedData}
              className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 font-medium flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Download Cleaned CSV
            </button>
            <button
              onClick={() => setImportStatus('idle')}
              className="bg-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-400 font-medium"
            >
              Start Over
            </button>
          </div>
        </div>
      )}

      {/* Success */}
      {importStatus === 'imported' && (
        <div className="text-center py-8">
          <CheckCircle className="mx-auto h-16 w-16 text-green-500 mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Import Complete!</h3>
          <p className="text-gray-600 mb-6">
            {stats?.validTrades} trades have been imported into your app
          </p>
          <button
            onClick={() => setImportStatus('idle')}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
          >
            Import Another File
          </button>
        </div>
      )}
    </div>
  );
};

export default CSVImportTool;
