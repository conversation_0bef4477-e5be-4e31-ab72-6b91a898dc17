import { Fragment as _Fragment, jsx as _jsx } from "react/jsx-runtime";
import { render, screen } from '@testing-library/react';
import { TradeAnalysis } from '../TradeAnalysis';
// Mock the API client
jest.mock('@adhd-trading-dashboard/shared', () => {
    const original = jest.requireActual('@adhd-trading-dashboard/shared');
    return {
        ...original,
        ApiProvider: ({ children }) => _jsx(_Fragment, { children: children }),
        useTradingData: () => ({
            data: {
                data: [],
            },
            isLoading: false,
            error: null,
            refetch: jest.fn(),
        }),
    };
});
// Mock the hooks
jest.mock('../../hooks/useTradeAnalysis', () => ({
    useTradeAnalysis: () => ({
        trades: [],
        filter: {},
        sort: { field: 'date', direction: 'desc' },
        page: 1,
        pageSize: 10,
        totalPages: 0,
        tradeSummary: {
            totalTrades: 0,
            winningTrades: 0,
            losingTrades: 0,
            breakEvenTrades: 0,
            totalProfit: 0,
            totalFees: 0,
            netProfit: 0,
            winRate: 0,
            averageWin: 0,
            averageLoss: 0,
            profitFactor: 0,
        },
        equityCurveData: [],
        distributionData: [],
        isLoading: false,
        error: null,
        setFilter: jest.fn(),
        clearFilters: jest.fn(),
        setSort: jest.fn(),
        setPage: jest.fn(),
        setPageSize: jest.fn(),
        fetchTrades: jest.fn(),
        exportTrades: jest.fn(),
    }),
}));
describe('TradeAnalysis', () => {
    it('renders without crashing', () => {
        render(_jsx(TradeAnalysis, {}));
        expect(screen.getByText('Trade Analysis')).toBeInTheDocument();
    });
    it('renders the filter component', () => {
        render(_jsx(TradeAnalysis, {}));
        expect(screen.getByText('Filter Trades')).toBeInTheDocument();
    });
    it('renders the summary component', () => {
        render(_jsx(TradeAnalysis, {}));
        expect(screen.getByText('Performance Summary')).toBeInTheDocument();
    });
    it('renders the table component', () => {
        render(_jsx(TradeAnalysis, {}));
        expect(screen.getByText('Trades')).toBeInTheDocument();
    });
    it('renders the export button', () => {
        render(_jsx(TradeAnalysis, {}));
        expect(screen.getByText('Export')).toBeInTheDocument();
    });
    it('renders the refresh button', () => {
        render(_jsx(TradeAnalysis, {}));
        expect(screen.getByText('Refresh')).toBeInTheDocument();
    });
});
//# sourceMappingURL=TradeAnalysis.test.js.map