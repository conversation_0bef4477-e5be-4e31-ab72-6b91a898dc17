{"version": 3, "file": "TradeAnalysis.test.js", "sourceRoot": "", "sources": ["TradeAnalysis.test.tsx"], "names": [], "mappings": ";AAIA,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AACxD,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAGjD,sBAAsB;AACtB,IAAI,CAAC,IAAI,CAAC,gCAAgC,EAAE,GAAG,EAAE;IAC/C,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,gCAAgC,CAAC,CAAC;IACtE,OAAO;QACL,GAAG,QAAQ;QACX,WAAW,EAAE,CAAC,EAAE,QAAQ,EAAiC,EAAE,EAAE,CAAC,4BAAG,QAAQ,GAAI;QAC7E,cAAc,EAAE,GAAG,EAAE,CAAC,CAAC;YACrB,IAAI,EAAE;gBACJ,IAAI,EAAE,EAAE;aACT;YACD,SAAS,EAAE,KAAK;YAChB,KAAK,EAAE,IAAI;YACX,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;SACnB,CAAC;KACH,CAAC;AACJ,CAAC,CAAC,CAAC;AAEH,iBAAiB;AACjB,IAAI,CAAC,IAAI,CAAC,8BAA8B,EAAE,GAAG,EAAE,CAAC,CAAC;IAC/C,gBAAgB,EAAE,GAAG,EAAE,CAAC,CAAC;QACvB,MAAM,EAAE,EAAE;QACV,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE;QAC1C,IAAI,EAAE,CAAC;QACP,QAAQ,EAAE,EAAE;QACZ,UAAU,EAAE,CAAC;QACb,YAAY,EAAE;YACZ,WAAW,EAAE,CAAC;YACd,aAAa,EAAE,CAAC;YAChB,YAAY,EAAE,CAAC;YACf,eAAe,EAAE,CAAC;YAClB,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,CAAC;YACZ,OAAO,EAAE,CAAC;YACV,UAAU,EAAE,CAAC;YACb,WAAW,EAAE,CAAC;YACd,YAAY,EAAE,CAAC;SAChB;QACD,eAAe,EAAE,EAAE;QACnB,gBAAgB,EAAE,EAAE;QACpB,SAAS,EAAE,KAAK;QAChB,KAAK,EAAE,IAAI;QACX,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;QACpB,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;QACvB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE;QAClB,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;QACtB,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE;QACtB,YAAY,EAAE,IAAI,CAAC,EAAE,EAAE;KACxB,CAAC;CACH,CAAC,CAAC,CAAC;AAEJ,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;IAC7B,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;QAClC,MAAM,CAAC,KAAC,aAAa,KAAG,CAAC,CAAC;QAC1B,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;IACjE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;QACtC,MAAM,CAAC,KAAC,aAAa,KAAG,CAAC,CAAC;QAC1B,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;IAChE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,+BAA+B,EAAE,GAAG,EAAE;QACvC,MAAM,CAAC,KAAC,aAAa,KAAG,CAAC,CAAC;QAC1B,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;IACtE,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,6BAA6B,EAAE,GAAG,EAAE;QACrC,MAAM,CAAC,KAAC,aAAa,KAAG,CAAC,CAAC;QAC1B,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;IACzD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,2BAA2B,EAAE,GAAG,EAAE;QACnC,MAAM,CAAC,KAAC,aAAa,KAAG,CAAC,CAAC;QAC1B,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;IACzD,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;QACpC,MAAM,CAAC,KAAC,aAAa,KAAG,CAAC,CAAC;QAC1B,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;IAC1D,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}