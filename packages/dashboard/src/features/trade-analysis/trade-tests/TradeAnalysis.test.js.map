{"version": 3, "file": "TradeAnalysis.test.js", "sourceRoot": "", "sources": ["TradeAnalysis.test.tsx"], "names": [], "mappings": ";AAOA,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,wBAAwB,CAAC;AAC5E,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAElD,OAAO,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AACzC,OAAO,EAAE,sBAAsB,EAAE,MAAM,8BAA8B,CAAC;AAEtE,eAAe;AACf,EAAE,CAAC,IAAI,CAAC,8BAA8B,EAAE,GAAG,EAAE,CAAC,CAAC;IAC7C,sBAAsB,EAAE,EAAE,CAAC,EAAE,EAAE;CAChC,CAAC,CAAC,CAAC;AAEJ,iBAAiB;AACjB,MAAM,SAAS,GAAG;IAChB,MAAM,EAAE;QACN,WAAW,EAAE,MAAM;QACnB,aAAa,EAAE,MAAM;QACrB,UAAU,EAAE,MAAM;QAClB,OAAO,EAAE,SAAS;QAClB,OAAO,EAAE,SAAS;QAClB,WAAW,EAAE,SAAS;QACtB,YAAY,EAAE,SAAS;QACvB,SAAS,EAAE,SAAS;QACpB,aAAa,EAAE,SAAS;QACxB,cAAc,EAAE,SAAS;QACzB,MAAM,EAAE,SAAS;QACjB,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,SAAS;QAClB,KAAK,EAAE,SAAS;QAChB,OAAO,EAAE,SAAS;QAClB,IAAI,EAAE,SAAS;QACf,MAAM,EAAE,SAAS;QACjB,MAAM,EAAE,SAAS;QACjB,WAAW,EAAE,SAAS;KACvB;IACD,OAAO,EAAE;QACP,GAAG,EAAE,KAAK;QACV,EAAE,EAAE,KAAK;QACT,EAAE,EAAE,MAAM;QACV,EAAE,EAAE,MAAM;QACV,EAAE,EAAE,MAAM;QACV,EAAE,EAAE,MAAM;QACV,GAAG,EAAE,MAAM;KACZ;IACD,SAAS,EAAE;QACT,EAAE,EAAE,MAAM;QACV,EAAE,EAAE,MAAM;QACV,EAAE,EAAE,MAAM;QACV,EAAE,EAAE,MAAM;QACV,EAAE,EAAE,MAAM;QACV,GAAG,EAAE,MAAM;KACZ;IACD,YAAY,EAAE;QACZ,EAAE,EAAE,KAAK;QACT,EAAE,EAAE,KAAK;QACT,EAAE,EAAE,KAAK;QACT,EAAE,EAAE,MAAM;QACV,EAAE,EAAE,MAAM;QACV,IAAI,EAAE,OAAO;QACb,MAAM,EAAE,KAAK;KACd;IACD,OAAO,EAAE;QACP,EAAE,EAAE,8BAA8B;QAClC,EAAE,EAAE,8BAA8B;QAClC,EAAE,EAAE,gCAAgC;KACrC;IACD,WAAW,EAAE;QACX,IAAI,EAAE,WAAW;QACjB,MAAM,EAAE,WAAW;QACnB,IAAI,EAAE,WAAW;KAClB;IACD,WAAW,EAAE;QACX,KAAK,EAAE,GAAG;QACV,OAAO,EAAE,GAAG;QACZ,MAAM,EAAE,GAAG;QACX,QAAQ,EAAE,GAAG;QACb,IAAI,EAAE,GAAG;KACV;IACD,WAAW,EAAE;QACX,KAAK,EAAE,GAAG;QACV,MAAM,EAAE,GAAG;QACX,OAAO,EAAE,GAAG;KACb;IACD,YAAY,EAAE;QACZ,IAAI,EAAE,mBAAmB;QACzB,OAAO,EAAE,mBAAmB;QAC5B,IAAI,EAAE,WAAW;KAClB;IACD,MAAM,EAAE;QACN,IAAI,EAAE,CAAC;QACP,OAAO,EAAE,EAAE;QACX,KAAK,EAAE,EAAE;QACT,OAAO,EAAE,EAAE;QACX,OAAO,EAAE,EAAE;QACX,KAAK,EAAE,EAAE;KACV;IACD,IAAI,EAAE,SAAS;IACf,WAAW,EAAE;QACX,EAAE,EAAE,OAAO;QACX,EAAE,EAAE,OAAO;QACX,EAAE,EAAE,OAAO;QACX,EAAE,EAAE,QAAQ;QACZ,EAAE,EAAE,QAAQ;KACb;CACF,CAAC;AAEF,YAAY;AACZ,MAAM,QAAQ,GAAG;IACf,MAAM,EAAE;QACN;YACE,EAAE,EAAE,GAAG;YACP,MAAM,EAAE,MAAM;YACd,SAAS,EAAE,MAAM;YACjB,UAAU,EAAE,MAAM;YAClB,SAAS,EAAE,MAAM;YACjB,QAAQ,EAAE,GAAG;YACb,SAAS,EAAE,sBAAsB;YACjC,QAAQ,EAAE,sBAAsB;YAChC,MAAM,EAAE,KAAK;YACb,UAAU,EAAE,GAAG;YACf,iBAAiB,EAAE,IAAI;YACvB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,SAAS;YAClB,QAAQ,EAAE,UAAU;YACpB,IAAI,EAAE,CAAC,aAAa,EAAE,UAAU,CAAC;SAClC;QACD;YACE,EAAE,EAAE,GAAG;YACP,MAAM,EAAE,MAAM;YACd,SAAS,EAAE,OAAO;YAClB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,MAAM;YACjB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE,sBAAsB;YACjC,QAAQ,EAAE,sBAAsB;YAChC,MAAM,EAAE,KAAK;YACb,UAAU,EAAE,KAAK;YACjB,iBAAiB,EAAE,IAAI;YACvB,SAAS,EAAE,KAAK;YAChB,OAAO,EAAE,SAAS;YAClB,QAAQ,EAAE,UAAU;YACpB,IAAI,EAAE,CAAC,WAAW,CAAC;SACpB;KACF;IACD,OAAO,EAAE;QACP,WAAW,EAAE,CAAC;QACd,aAAa,EAAE,CAAC;QAChB,YAAY,EAAE,CAAC;QACf,SAAS,EAAE,CAAC;QACZ,OAAO,EAAE,GAAG;QACZ,UAAU,EAAE,MAAM;QAClB,WAAW,EAAE,CAAC;QACd,YAAY,EAAE,QAAQ;QACtB,eAAe,EAAE,KAAK;QACtB,UAAU,EAAE,GAAG;QACf,WAAW,EAAE,CAAC;QACd,eAAe,EAAE,GAAG;QACpB,UAAU,EAAE,MAAM;KACnB;IACD,iBAAiB,EAAE;QACjB;YACE,QAAQ,EAAE,QAAQ;YAClB,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,GAAG;YACZ,UAAU,EAAE,GAAG;YACf,iBAAiB,EAAE,GAAG;SACvB;QACD;YACE,QAAQ,EAAE,QAAQ;YAClB,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,GAAG;YACZ,UAAU,EAAE,KAAK;YACjB,iBAAiB,EAAE,KAAK;SACzB;KACF;IACD,mBAAmB,EAAE;QACnB;YACE,QAAQ,EAAE,UAAU;YACpB,KAAK,EAAE,UAAU;YACjB,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,GAAG;YACZ,UAAU,EAAE,GAAG;YACf,iBAAiB,EAAE,GAAG;SACvB;QACD;YACE,QAAQ,EAAE,UAAU;YACpB,KAAK,EAAE,UAAU;YACjB,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,GAAG;YACZ,UAAU,EAAE,KAAK;YACjB,iBAAiB,EAAE,KAAK;SACzB;KACF;IACD,oBAAoB,EAAE;QACpB;YACE,QAAQ,EAAE,WAAW;YACrB,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,GAAG;YACZ,UAAU,EAAE,GAAG;YACf,iBAAiB,EAAE,GAAG;SACvB;QACD;YACE,QAAQ,EAAE,WAAW;YACrB,KAAK,EAAE,KAAK;YACZ,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,GAAG;YACZ,UAAU,EAAE,KAAK;YACjB,iBAAiB,EAAE,KAAK;SACzB;KACF;IACD,kBAAkB,EAAE;QAClB;YACE,QAAQ,EAAE,SAAS;YACnB,KAAK,EAAE,SAAS;YAChB,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,GAAG;YACZ,UAAU,EAAE,KAAK;YACjB,iBAAiB,EAAE,MAAM;SAC1B;KACF;IACD,oBAAoB,EAAE;QACpB;YACE,QAAQ,EAAE,YAAY;YACtB,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,GAAG;YACZ,UAAU,EAAE,GAAG;SAChB;QACD;YACE,QAAQ,EAAE,aAAa;YACvB,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,GAAG;YACZ,UAAU,EAAE,KAAK;SAClB;KACF;IACD,oBAAoB,EAAE;QACpB;YACE,QAAQ,EAAE,QAAQ;YAClB,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,GAAG;YACZ,UAAU,EAAE,GAAG;SAChB;QACD;YACE,QAAQ,EAAE,SAAS;YACnB,MAAM,EAAE,CAAC;YACT,OAAO,EAAE,GAAG;YACZ,UAAU,EAAE,KAAK;SAClB;KACF;CACF,CAAC;AAEF,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;IAC7B,UAAU,CAAC,GAAG,EAAE;QACd,EAAE,CAAC,aAAa,EAAE,CAAC;QAClB,sBAA8B,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAC9D,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;QACjE,MAAM,CACJ,KAAC,aAAa,IAAC,KAAK,EAAE,SAAS,YAC7B,KAAC,aAAa,KAAG,GACH,CACjB,CAAC;QAEF,mCAAmC;QACnC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAE/D,wBAAwB;QACxB,MAAM,OAAO,CAAC,GAAG,EAAE;YACjB,mDAAmD;YACnD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,+CAA+C;QAC/C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC1D,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAEzD,wBAAwB;QACxB,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE5C,uCAAuC;QACvC,MAAM,OAAO,CAAC,GAAG,EAAE;YACjB,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YAC1D,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACvD,CAAC,CAAC,CAAC;QAEH,yBAAyB;QACzB,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;QAE7C,6CAA6C;QAC7C,MAAM,OAAO,CAAC,GAAG,EAAE;YACjB,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QACxE,CAAC,CAAC,CAAC;QAEH,4BAA4B;QAC5B,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC;QAEhD,+CAA+C;QAC/C,MAAM,OAAO,CAAC,GAAG,EAAE;YACjB,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC1E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;QAC7C,iBAAiB;QAChB,sBAA8B,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;QAE1E,MAAM,CACJ,KAAC,aAAa,IAAC,KAAK,EAAE,SAAS,YAC7B,KAAC,aAAa,KAAG,GACH,CACjB,CAAC;QAEF,iCAAiC;QACjC,MAAM,OAAO,CAAC,GAAG,EAAE;YACjB,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}