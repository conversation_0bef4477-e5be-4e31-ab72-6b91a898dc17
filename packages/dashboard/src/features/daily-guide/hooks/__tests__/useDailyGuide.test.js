import { jsx as _jsx } from "react/jsx-runtime";
/**
 * Use Daily Guide Hook Tests
 */
import { renderHook, act } from '@testing-library/react';
import { useDailyGuide } from '../useDailyGuide';
import { DailyGuideProvider } from '../../state';
import { vi, describe, it, expect, beforeEach } from 'vitest';
// Mock the fetch function
global.fetch = vi.fn();
// Mock the state
vi.mock('../../state', () => {
    return {
        DailyGuideProvider: ({ children }) => children,
        useDailyGuideStore: () => ({
            state: {
                data: {
                    marketOverview: null,
                    tradingPlan: null,
                    keyPriceLevels: [],
                    watchlist: [],
                    marketNews: [],
                },
                isLoading: false,
                error: null,
                selectedDate: '2023-01-01',
            },
            dispatch: vi.fn(),
        }),
        useDailyGuideSelector: (selector) => selector({
            data: {
                marketOverview: null,
                tradingPlan: null,
                keyPriceLevels: [],
                watchlist: [],
                marketNews: [],
            },
            isLoading: false,
            error: null,
            selectedDate: '2023-01-01',
        }),
        useDailyGuideActions: () => ({
            fetchDataStart: vi.fn(),
            fetchDataSuccess: vi.fn(),
            fetchDataError: vi.fn(),
            updateTradingPlanItem: vi.fn(),
            addTradingPlanItem: vi.fn(),
            removeTradingPlanItem: vi.fn(),
            updateSelectedDate: vi.fn(),
            updateMarketOverview: vi.fn(),
            updateKeyPriceLevels: vi.fn(),
            resetState: vi.fn(),
        }),
        dailyGuideActions: {
            fetchDataStart: vi.fn(),
            fetchDataSuccess: vi.fn(),
            fetchDataError: vi.fn(),
            updateTradingPlanItem: vi.fn(),
            addTradingPlanItem: vi.fn(),
            removeTradingPlanItem: vi.fn(),
            updateSelectedDate: vi.fn(),
            updateMarketOverview: vi.fn(),
            updateKeyPriceLevels: vi.fn(),
            resetState: vi.fn(),
        },
    };
});
// Create a wrapper component
const wrapper = ({ children }) => (_jsx(DailyGuideProvider, { children: children }));
describe('useDailyGuide', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });
    it('should return the initial state', () => {
        const { result } = renderHook(() => useDailyGuide(), { wrapper });
        expect(result.current.selectedDate).toBe('2023-01-01');
        expect(result.current.marketOverview).toBeNull();
        expect(result.current.tradingPlan).toBeNull();
        expect(result.current.keyPriceLevels).toEqual([]);
        expect(result.current.isLoading).toBe(false);
        expect(result.current.error).toBeNull();
    });
    it('should fetch data on mount', () => {
        const { result } = renderHook(() => useDailyGuide(), { wrapper });
        // Check that the fetchData function was called
        expect(result.current.isLoading).toBe(false);
    });
    it('should handle date change', () => {
        const { result } = renderHook(() => useDailyGuide(), { wrapper });
        // Call the onDateChange function
        act(() => {
            result.current.onDateChange('2023-01-02');
        });
        // Check that the updateSelectedDate action was called
        expect(result.current.selectedDate).toBe('2023-01-01'); // Mock doesn't update the state
    });
    it('should handle trading plan item toggle', () => {
        const { result } = renderHook(() => useDailyGuide(), { wrapper });
        // Call the onTradingPlanItemToggle function
        act(() => {
            result.current.onTradingPlanItemToggle('1', true);
        });
        // Check that the updateTradingPlanItem action was called
        expect(result.current.tradingPlan).toBeNull(); // Mock doesn't update the state
    });
    it('should handle refresh', () => {
        const { result } = renderHook(() => useDailyGuide(), { wrapper });
        // Call the onRefresh function
        act(() => {
            result.current.onRefresh();
        });
        // Check that the fetchData function was called
        expect(result.current.isLoading).toBe(false); // Mock doesn't update the state
    });
});
//# sourceMappingURL=useDailyGuide.test.js.map