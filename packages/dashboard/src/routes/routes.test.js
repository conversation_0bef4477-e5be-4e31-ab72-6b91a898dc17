import { jsx as _jsx } from "react/jsx-runtime";
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { MemoryRouter } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';
import { f1Theme } from '@adhd-trading-dashboard/shared';
import { AppRoutes } from '../routes';
// Mock the lazy-loaded components
vi.mock('../pages/Dashboard', () => ({
    default: () => _jsx("div", { "data-testid": "dashboard-page", children: "Dashboard Page" }),
}));
vi.mock('../pages/TradeJournal', () => ({
    default: () => _jsx("div", { "data-testid": "trade-journal-page", children: "Trade Journal Page" }),
}));
vi.mock('../pages/TradeForm', () => ({
    default: () => _jsx("div", { "data-testid": "trade-form-page", children: "Trade Form Page" }),
}));
vi.mock('../pages/NotFound', () => ({
    default: () => _jsx("div", { "data-testid": "not-found-page", children: "Not Found Page" }),
}));
// Mock other components
vi.mock('./layouts/MainLayout', () => ({
    default: ({ children }) => (_jsx("div", { "data-testid": "main-layout", children: children })),
}));
vi.mock('./components/molecules/LoadingScreen', () => ({
    default: () => _jsx("div", { "data-testid": "loading-screen", children: "Loading..." }),
}));
describe('AppRoutes Component', () => {
    beforeEach(() => {
        // Reset mocks before each test
        vi.clearAllMocks();
    });
    it('renders the dashboard page for the root route', async () => {
        render(_jsx(ThemeProvider, { theme: f1Theme, children: _jsx(MemoryRouter, { initialEntries: ['/'], children: _jsx(AppRoutes, {}) }) }));
        // Wait for the lazy-loaded component to render
        await screen.findByTestId('dashboard-page');
        expect(screen.getByTestId('dashboard-page')).toBeInTheDocument();
    });
    it('renders the trade journal page for the /journal route', async () => {
        render(_jsx(ThemeProvider, { theme: f1Theme, children: _jsx(MemoryRouter, { initialEntries: ['/journal'], children: _jsx(AppRoutes, {}) }) }));
        // Wait for the lazy-loaded component to render
        await screen.findByTestId('trade-journal-page');
        expect(screen.getByTestId('trade-journal-page')).toBeInTheDocument();
    });
    it('renders the trade form page for the /trade/new route', async () => {
        render(_jsx(ThemeProvider, { theme: f1Theme, children: _jsx(MemoryRouter, { initialEntries: ['/trade/new'], children: _jsx(AppRoutes, {}) }) }));
        // Wait for the lazy-loaded component to render
        await screen.findByTestId('trade-form-page');
        expect(screen.getByTestId('trade-form-page')).toBeInTheDocument();
    });
    it('renders the trade form page for the /trade/edit/:id route', async () => {
        render(_jsx(ThemeProvider, { theme: f1Theme, children: _jsx(MemoryRouter, { initialEntries: ['/trade/edit/123'], children: _jsx(AppRoutes, {}) }) }));
        // Wait for the lazy-loaded component to render
        await screen.findByTestId('trade-form-page');
        expect(screen.getByTestId('trade-form-page')).toBeInTheDocument();
    });
    it('renders the trade form page for the /trade/:id route', async () => {
        render(_jsx(ThemeProvider, { theme: f1Theme, children: _jsx(MemoryRouter, { initialEntries: ['/trade/123'], children: _jsx(AppRoutes, {}) }) }));
        // Wait for the lazy-loaded component to render
        await screen.findByTestId('trade-form-page');
        expect(screen.getByTestId('trade-form-page')).toBeInTheDocument();
    });
    it('renders the not found page for an unknown route', async () => {
        render(_jsx(ThemeProvider, { theme: f1Theme, children: _jsx(MemoryRouter, { initialEntries: ['/unknown-route'], children: _jsx(AppRoutes, {}) }) }));
        // Wait for the lazy-loaded component to render
        await screen.findByTestId('not-found-page');
        expect(screen.getByTestId('not-found-page')).toBeInTheDocument();
    });
    it('correctly prioritizes /trade/edit/:id over /trade/:id', async () => {
        // This test ensures that /trade/edit/123 is not matched by /trade/:id
        render(_jsx(ThemeProvider, { theme: f1Theme, children: _jsx(MemoryRouter, { initialEntries: ['/trade/edit/123'], children: _jsx(AppRoutes, {}) }) }));
        // Wait for the lazy-loaded component to render
        await screen.findByTestId('trade-form-page');
        expect(screen.getByTestId('trade-form-page')).toBeInTheDocument();
        // We can't directly test that the route was matched correctly,
        // but we can check that the component rendered
    });
});
//# sourceMappingURL=routes.test.js.map