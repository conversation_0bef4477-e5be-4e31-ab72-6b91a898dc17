/**
 * Analyze component and provide refactoring suggestions
 */
export function analyzeComponent(componentPath: any, options?: {}): Promise<{
    file: {
        path: any;
        name: string;
        size: any;
        lines: any;
    };
    metrics: {
        imports: any;
        exports: any;
        hooks: any;
        components: any;
        styledComponents: any;
        complexity: number;
        responsibilities: number;
    };
    patterns: any[];
    issues: {
        type: string;
        category: string;
        message: string;
        suggestion: string;
    }[];
    suggestions: never[];
    score: number;
} | null | undefined>;
//# sourceMappingURL=component-analyzer.d.ts.map