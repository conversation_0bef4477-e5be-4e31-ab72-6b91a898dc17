import { jsx as _jsx } from "react/jsx-runtime";
import { render, screen } from '@testing-library/react';
import { ThemeProvider } from '../../../theme';
import { ErrorBoundary } from '../ErrorBoundary';
import { vi, describe, it, expect, beforeAll, afterAll } from 'vitest';
// Mock console.error to avoid test output noise
const originalConsoleError = console.error;
beforeAll(() => {
    console.error = vi.fn();
});
afterAll(() => {
    console.error = originalConsoleError;
});
// Component that throws an error
const ErrorThrowingComponent = ({ shouldThrow = true }) => {
    if (shouldThrow) {
        throw new Error('Test error');
    }
    return _jsx("div", { children: "No error" });
};
describe('ErrorBoundary', () => {
    it('renders children when there is no error', () => {
        render(_jsx(ThemeProvider, { children: _jsx(ErrorBoundary, { children: _jsx("div", { "data-testid": "child", children: "Child content" }) }) }));
        expect(screen.getByTestId('child')).toBeInTheDocument();
        expect(screen.getByText('Child content')).toBeInTheDocument();
    });
    it('renders the default fallback UI when an error occurs', () => {
        // Suppress React's error boundary warning in test output
        const spy = vi.spyOn(console, 'error');
        spy.mockImplementation(() => { });
        render(_jsx(ThemeProvider, { children: _jsx(ErrorBoundary, { children: _jsx(ErrorThrowingComponent, {}) }) }));
        expect(screen.getByText('Something went wrong')).toBeInTheDocument();
        expect(screen.getByText('Test error')).toBeInTheDocument();
        expect(screen.getByText('Reload Application')).toBeInTheDocument();
        spy.mockRestore();
    });
    it('renders a custom fallback when provided', () => {
        // Suppress React's error boundary warning in test output
        const spy = vi.spyOn(console, 'error');
        spy.mockImplementation(() => { });
        render(_jsx(ThemeProvider, { children: _jsx(ErrorBoundary, { fallback: _jsx("div", { "data-testid": "custom-fallback", children: "Custom error UI" }), children: _jsx(ErrorThrowingComponent, {}) }) }));
        expect(screen.getByTestId('custom-fallback')).toBeInTheDocument();
        expect(screen.getByText('Custom error UI')).toBeInTheDocument();
        spy.mockRestore();
    });
    it('calls onError when an error occurs', () => {
        // Suppress React's error boundary warning in test output
        const spy = vi.spyOn(console, 'error');
        spy.mockImplementation(() => { });
        const onError = vi.fn();
        render(_jsx(ThemeProvider, { children: _jsx(ErrorBoundary, { onError: onError, children: _jsx(ErrorThrowingComponent, {}) }) }));
        expect(onError).toHaveBeenCalledTimes(1);
        expect(onError).toHaveBeenCalledWith(expect.objectContaining({ message: 'Test error' }), expect.objectContaining({ componentStack: expect.any(String) }));
        spy.mockRestore();
    });
});
//# sourceMappingURL=ErrorBoundary.test.js.map