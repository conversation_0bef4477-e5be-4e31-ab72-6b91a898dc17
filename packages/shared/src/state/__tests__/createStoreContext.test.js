import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { render, screen, fireEvent } from '@testing-library/react';
import { createStoreContext } from '../createStoreContext';
// Define test reducer
const testReducer = (state, action) => {
    switch (action.type) {
        case 'INCREMENT':
            return {
                ...state,
                count: state.count + (action.payload || 1),
            };
        case 'DECREMENT':
            return {
                ...state,
                count: state.count - (action.payload || 1),
            };
        case 'SET_TEXT':
            return {
                ...state,
                text: action.payload,
            };
        default:
            return state;
    }
};
// Define initial state
const initialState = {
    count: 0,
    text: '',
};
describe('createStoreContext', () => {
    it('should create a context and provider', () => {
        const { Provider } = createStoreContext(testReducer, initialState);
        const TestComponent = () => _jsx("div", { children: "Test" });
        render(_jsx(Provider, { children: _jsx(TestComponent, {}) }));
        expect(screen.getByText('Test')).toBeInTheDocument();
    });
    it('should provide state and dispatch through useStore', () => {
        const { Provider, useStore } = createStoreContext(testReducer, initialState);
        const TestComponent = () => {
            const { state, dispatch } = useStore();
            return (_jsxs("div", { children: [_jsx("div", { "data-testid": "count", children: state.count }), _jsx("button", { onClick: () => dispatch({ type: 'INCREMENT' }), children: "Increment" })] }));
        };
        render(_jsx(Provider, { children: _jsx(TestComponent, {}) }));
        expect(screen.getByTestId('count')).toHaveTextContent('0');
        fireEvent.click(screen.getByText('Increment'));
        expect(screen.getByTestId('count')).toHaveTextContent('1');
    });
    it('should provide state through useSelector', () => {
        const { Provider, useSelector } = createStoreContext(testReducer, initialState);
        const TestComponent = () => {
            const count = useSelector(state => state.count);
            const text = useSelector(state => state.text);
            return (_jsxs("div", { children: [_jsx("div", { "data-testid": "count", children: count }), _jsx("div", { "data-testid": "text", children: text || 'empty' })] }));
        };
        render(_jsx(Provider, { children: _jsx(TestComponent, {}) }));
        expect(screen.getByTestId('count')).toHaveTextContent('0');
        expect(screen.getByTestId('text')).toHaveTextContent('empty');
    });
    it('should provide actions through useAction', () => {
        const { Provider, useStore, useAction } = createStoreContext(testReducer, initialState);
        const TestComponent = () => {
            const { state } = useStore();
            const increment = useAction((amount = 1) => ({ type: 'INCREMENT', payload: amount }));
            const setText = useAction((text) => ({ type: 'SET_TEXT', payload: text }));
            return (_jsxs("div", { children: [_jsx("div", { "data-testid": "count", children: state.count }), _jsx("div", { "data-testid": "text", children: state.text || 'empty' }), _jsx("button", { onClick: () => increment(5), children: "Increment by 5" }), _jsx("button", { onClick: () => setText('Hello'), children: "Set Text" })] }));
        };
        render(_jsx(Provider, { children: _jsx(TestComponent, {}) }));
        expect(screen.getByTestId('count')).toHaveTextContent('0');
        expect(screen.getByTestId('text')).toHaveTextContent('empty');
        fireEvent.click(screen.getByText('Increment by 5'));
        expect(screen.getByTestId('count')).toHaveTextContent('5');
        fireEvent.click(screen.getByText('Set Text'));
        expect(screen.getByTestId('text')).toHaveTextContent('Hello');
    });
    it('should provide multiple actions through useActions', () => {
        const { Provider, useStore, useActions } = createStoreContext(testReducer, initialState);
        const actionCreators = {
            increment: (amount = 1) => ({ type: 'INCREMENT', payload: amount }),
            decrement: (amount = 1) => ({ type: 'DECREMENT', payload: amount }),
            setText: (text) => ({ type: 'SET_TEXT', payload: text }),
        };
        const TestComponent = () => {
            const { state } = useStore();
            const actions = useActions(actionCreators);
            return (_jsxs("div", { children: [_jsx("div", { "data-testid": "count", children: state.count }), _jsx("button", { onClick: () => actions.increment(3), children: "Increment by 3" }), _jsx("button", { onClick: () => actions.decrement(2), children: "Decrement by 2" })] }));
        };
        render(_jsx(Provider, { children: _jsx(TestComponent, {}) }));
        expect(screen.getByTestId('count')).toHaveTextContent('0');
        fireEvent.click(screen.getByText('Increment by 3'));
        expect(screen.getByTestId('count')).toHaveTextContent('3');
        fireEvent.click(screen.getByText('Decrement by 2'));
        expect(screen.getByTestId('count')).toHaveTextContent('1');
    });
    it('should allow overriding initial state', () => {
        const { Provider, useSelector } = createStoreContext(testReducer, initialState);
        const TestComponent = () => {
            const count = useSelector(state => state.count);
            return _jsx("div", { "data-testid": "count", children: count });
        };
        render(_jsx(Provider, { initialState: { count: 10, text: 'Initial' }, children: _jsx(TestComponent, {}) }));
        expect(screen.getByTestId('count')).toHaveTextContent('10');
    });
});
//# sourceMappingURL=createStoreContext.test.js.map