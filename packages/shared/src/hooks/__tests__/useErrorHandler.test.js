/**
 * useErrorHandler Tests
 */
import { renderHook, act } from '@testing-library/react';
import { useErrorHandler } from '../useErrorHandler';
import { vi, describe, it, expect, beforeAll, afterAll } from 'vitest';
// Mock console.error to avoid test output noise
const originalConsoleError = console.error;
beforeAll(() => {
    console.error = vi.fn();
});
afterAll(() => {
    console.error = originalConsoleError;
});
describe('useErrorHandler', () => {
    it('should initialize with no error', () => {
        const { result } = renderHook(() => useErrorHandler());
        expect(result.current.error).toBeNull();
        expect(result.current.hasError).toBe(false);
    });
    it('should handle errors', () => {
        const { result } = renderHook(() => useErrorHandler());
        const testError = new Error('Test error');
        act(() => {
            result.current.handleError(testError);
        });
        expect(result.current.error).toBe(testError);
        expect(result.current.hasError).toBe(true);
        expect(console.error).toHaveBeenCalledWith('Error caught by useErrorHandler:', testError);
    });
    it('should reset errors', () => {
        const { result } = renderHook(() => useErrorHandler());
        const testError = new Error('Test error');
        act(() => {
            result.current.handleError(testError);
        });
        expect(result.current.hasError).toBe(true);
        act(() => {
            result.current.resetError();
        });
        expect(result.current.error).toBeNull();
        expect(result.current.hasError).toBe(false);
    });
    it('should execute functions and handle errors with tryExecute', async () => {
        const { result } = renderHook(() => useErrorHandler());
        // Test successful execution
        let successResult;
        await act(async () => {
            successResult = await result.current.tryExecute(() => Promise.resolve('success'));
        });
        expect(successResult).toBe('success');
        expect(result.current.hasError).toBe(false);
        // Test error handling
        const testError = new Error('Test error');
        let errorResult;
        await act(async () => {
            errorResult = await result.current.tryExecute(() => Promise.reject(testError));
        });
        expect(errorResult).toBeUndefined();
        expect(result.current.hasError).toBe(true);
        expect(result.current.error).toBe(testError);
    });
});
//# sourceMappingURL=useErrorHandler.test.js.map