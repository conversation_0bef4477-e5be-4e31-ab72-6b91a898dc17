declare function _exports(env: any, argv: any): {
    mode: string;
    entry: string;
    output: {
        path: string;
        filename: string;
        library: {
            name: string;
            type: string;
        };
        globalObject: string;
    };
    devtool: string;
    resolve: {
        extensions: string[];
    };
    externals: {
        react: {
            commonjs: string;
            commonjs2: string;
            amd: string;
            root: string;
        };
        'react-dom': {
            commonjs: string;
            commonjs2: string;
            amd: string;
            root: string;
        };
        'styled-components': {
            commonjs: string;
            commonjs2: string;
            amd: string;
            root: string;
        };
    };
    module: {
        rules: ({
            test: RegExp;
            exclude: RegExp;
            use: {
                loader: string;
                options: {
                    cacheDirectory: boolean;
                };
            }[];
        } | {
            test: RegExp;
            use: string[];
            exclude?: undefined;
        })[];
    };
    optimization: {
        minimize: boolean;
        minimizer: TerserPlugin<import("terser").MinifyOptions>[];
    };
};
export = _exports;
import TerserPlugin = require("terser-webpack-plugin");
//# sourceMappingURL=webpack.config.d.ts.map